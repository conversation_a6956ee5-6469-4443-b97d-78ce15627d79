import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
// Temporarily disabled to debug SSR issues
// import { ImagePreloader } from "@/components/game/ImagePreloader";

export const metadata: Metadata = {
  title: "Recognition Combine",
  description: "Test your NFL player recognition skills",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className="font-sans antialiased">
        {/* Temporarily disabled to debug SSR issues */}
        {/* <ImagePreloader /> */}
        {/* Global Animation Portal Container - highest z-index, no constraints */}
        <div id="global-animation-portal" className="fixed inset-0 pointer-events-none z-[9999]" />
        {/*
          <script
            dangerouslySetInnerHTML={{
              __html: `
                // Filter out Chrome extension errors from console
                const originalError = console.error;
                console.error = function(...args) {
                  const message = args.join(' ');
                  if (message.includes('chrome-extension://') ||
                      message.includes('executionId') ||
                      message.includes('Invalid response from client')) {
                    return; // Suppress extension-related errors
                  }
                  originalError.apply(console, args);
                };
              `
            }}
          />
        */}
        {children}
      </body>
    </html>
  );
}
