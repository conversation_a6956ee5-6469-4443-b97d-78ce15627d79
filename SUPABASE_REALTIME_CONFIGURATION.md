# Supabase Realtime Configuration Fix

## Problem
The application was experiencing "Unknown error" when subscribing to global game_rooms changes. This is a permissions issue where Supabase Realtime doesn't have proper authorization to broadcast changes from certain tables.

## Root Cause
By default, Supabase Realtime only broadcasts changes from tables that are explicitly added to the `supabase_realtime` publication. Without this configuration, authenticated clients cannot subscribe to postgres_changes events on these tables.

## Solution

### 1. Database Migration (Automated)
The migration file `supabase/migrations/20250617000000_fix_realtime_publications.sql` has been created to:
- Add `game_rooms` and `game_players` tables to the `supabase_realtime` publication
- Ensure proper RLS policies exist for realtime access
- Grant necessary permissions for realtime subscriptions

### 2. Manual Dashboard Configuration (If Needed)
If the migration doesn't fully resolve the issue, you can manually configure the publication through the Supabase Dashboard:

1. **Navigate to Database → Replication**
   - Go to your Supabase project dashboard
   - Click on "Database" in the left sidebar
   - Click on "Replication"

2. **Configure the supabase_realtime Publication**
   - You'll see a publication named `supabase_realtime`
   - Click on the table count link next to it (e.g., "2 tables")
   - This opens the publication configuration screen

3. **Enable Tables for Realtime**
   - Make sure the following tables are toggled ON:
     - `game_rooms`
     - `game_players`
   - Click "Save" to apply the changes

4. **Verify Configuration**
   - The publication should now include both tables
   - Realtime subscriptions should work without "Unknown error"

### 3. Alternative SQL Commands
If you prefer to run SQL commands directly, you can execute these in the SQL Editor:

```sql
-- Add tables to realtime publication
ALTER PUBLICATION supabase_realtime ADD TABLE game_rooms;
ALTER PUBLICATION supabase_realtime ADD TABLE game_players;

-- Verify the publication includes the tables
SELECT schemaname, tablename 
FROM pg_publication_tables 
WHERE pubname = 'supabase_realtime';
```

## Expected Results
After applying this fix:
- ✅ Global subscriptions to `game_rooms` changes will work without errors
- ✅ Global subscriptions to `game_players` changes will work without errors
- ✅ The console error "Error subscribing to global game_rooms changes: Unknown error" will be resolved
- ✅ Realtime updates for lobby refreshes will function properly

## Testing
To verify the fix is working:
1. Open the browser console
2. Join/leave rooms or create new rooms
3. Check that there are no "Unknown error" messages in the realtime subscription logs
4. Verify that lobby updates happen in real-time when other users join/leave rooms

## Notes
- This configuration is safe and only affects the ability to receive realtime notifications
- It doesn't change any security policies or data access permissions
- The existing RLS policies still control what data users can actually see and modify
