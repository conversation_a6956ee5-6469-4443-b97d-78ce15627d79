# Deploy player-disconnect-janitor Edge Function
# This script deploys the player disconnect janitor function to Supabase

Write-Host "Deploying player-disconnect-janitor Edge Function..." -ForegroundColor Green

try {
    # Deploy the function
    supabase functions deploy player-disconnect-janitor --project-ref xmyxuvuimebjltnaamox
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ player-disconnect-janitor deployed successfully!" -ForegroundColor Green
        Write-Host "The function will run every minute to mark stale players as disconnected." -ForegroundColor Yellow
    } else {
        Write-Host "❌ Deployment failed with exit code: $LASTEXITCODE" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Error during deployment: $_" -ForegroundColor Red
    exit 1
}

Write-Host "Deployment complete!" -ForegroundColor Green
