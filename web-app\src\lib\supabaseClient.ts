import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('[SupabaseClient] Environment check:', {
  hasSupabaseUrl: !!supabaseUrl,
  supabaseUrlValue: supabaseUrl,
  supabaseUrlType: typeof supabaseUrl,
  hasSupabaseAnonKey: !!supabaseAnonKey,
  supabaseAnonKeyLength: supabaseAnonKey ? supabaseAnonKey.length : 0,
  nodeEnv: process.env.NODE_ENV
});

if (!supabaseUrl) {
  const errorMsg = `Client-side: Missing env.NEXT_PUBLIC_SUPABASE_URL. Current value: "${supabaseUrl}"`;
  console.error('[SupabaseClient]', errorMsg);
  throw new Error(errorMsg);
}

if (!supabaseAnonKey) {
  const errorMsg = `Client-side: Missing env.NEXT_PUBLIC_SUPABASE_ANON_KEY. Has value: ${!!supabaseAnonKey}`;
  console.error('[SupabaseClient]', errorMsg);
  throw new Error(errorMsg);
}

// Additional URL validation
try {
  new URL(supabaseUrl);
} catch (urlError) {
  const errorMsg = `Client-side: Invalid SUPABASE_URL format: "${supabaseUrl}". Error: ${urlError}`;
  console.error('[SupabaseClient]', errorMsg);
  throw new Error(errorMsg);
}

console.log('[SupabaseClient] Creating Supabase client with URL:', supabaseUrl.substring(0, 30) + '...');

export const supabase = createClient(supabaseUrl, supabaseAnonKey); 