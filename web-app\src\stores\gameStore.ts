import { create } from 'zustand';
import type { PlayerData, PlayerQuestion, RecentAnswer, GameModeType } from '@/types';
import { loadPlayerData, generateQuestion } from '@/lib/playerData';

// Constants
const INITIAL_TIMED_MODE_DURATION = 60; // seconds
const MAX_TIMER_CAP = 60; // seconds

// Helper for logging
const logging = {
  info: (message: string) => console.log(`[GameStore] INFO: ${message}`),
  warning: (message: string) => console.warn(`[GameStore] WARN: ${message}`),
  error: (message: string) => console.error(`[GameStore] ERROR: ${message}`),
};

interface GameState {
  // Core game state
  players: PlayerData[];
  currentQuestion: PlayerQuestion | null;
  gameStatus: 'idle' | 'loading' | 'countdown' | 'playing' | 'finished';
  activeGameMode: GameModeType;
  
  // Scores and tracking
  score: number;
  timedScore: number;
  streak: number;
  bestStreak: number;
  bestNormalScore: number;
  bestTimedScore: number;
  correctCount: number;
  totalAsked: number;
  
  // Answer state
  isAnswered: boolean;
  lastAnswerCorrect: boolean | null;
  userChoiceName: string | null;
  recentAnswers: RecentAnswer[];
  
  // UI state
  askedPlayerIds: Set<number>;
  lastScoreChange: number | null;
  animationTrigger: number;
  nextQuestionImageToPreload: string | null;
  lastTimeChange: number | null;
  timeChangeAnimationTrigger: number;
  
  // Loading state
  isLoadingInitialGame: boolean;
  loadingMessage: string;
  isInitialQuestionReady: boolean;
  
  // Timed mode state
  timer: number;
  isCountdownActive: boolean;
  countdownValue: number;
  _timerIntervalId: NodeJS.Timeout | null;
  
  // Actions
  loadPlayers: () => Promise<void>;
  setGameMode: (mode: GameModeType) => void;
  startCountdown: () => void;
  _tickCountdown: () => void;
  _initializeTimedModeGame: () => void;
  _initializeNormalModeGame: () => void;
  nextQuestion: () => void;
  submitAnswer: (choiceName: string) => void;
  resetCurrentModeGame: () => void;
  _startTimerInterval: () => void;
  _stopTimerInterval: () => void;
  _tickTimer: () => void;
}

// Persist best scores to localStorage
const getStoredBestScores = () => {
  if (typeof window !== 'undefined') {
    const normal = localStorage.getItem('bestNormalScore');
    const timed = localStorage.getItem('bestTimedScore');
    return {
      bestNormalScore: normal ? parseInt(normal, 10) : 0,
      bestTimedScore: timed ? parseInt(timed, 10) : 0,
    };
  }
  return { bestNormalScore: 0, bestTimedScore: 0 };
};

const storedBestScores = getStoredBestScores();

export const useGameStore = create<GameState>((set, get) => ({
  // Initial state
  players: [],
  currentQuestion: null,
  score: 0,
  timedScore: 0,
  streak: 0,
  bestStreak: 0,
  bestNormalScore: storedBestScores.bestNormalScore,
  bestTimedScore: storedBestScores.bestTimedScore,
  correctCount: 0,
  totalAsked: 0,
  isAnswered: false,
  lastAnswerCorrect: null,
  userChoiceName: null,
  recentAnswers: [],
  gameStatus: 'idle',
  activeGameMode: 'normal',
  askedPlayerIds: new Set(),
  lastScoreChange: null,
  animationTrigger: 0,
  nextQuestionImageToPreload: null,
  lastTimeChange: null,
  timeChangeAnimationTrigger: 0,
  isLoadingInitialGame: true,
  loadingMessage: "Initializing Game...",
  isInitialQuestionReady: false,
  timer: INITIAL_TIMED_MODE_DURATION,
  isCountdownActive: false,
  countdownValue: 3,
  _timerIntervalId: null,

  loadPlayers: async () => {
    set({ isLoadingInitialGame: true, loadingMessage: "Initializing Game...", activeGameMode: 'normal' });
    const players = await loadPlayerData();
    set({ players, loadingMessage: "Preparing Game..." });
    logging.info("Players loaded in store");
    get()._initializeNormalModeGame(); // Start with normal mode
    set({ isLoadingInitialGame: false, isInitialQuestionReady: true });
  },

  setGameMode: (mode) => {
    const currentMode = get().activeGameMode;
    if (currentMode === mode && get().gameStatus === 'playing') {
      logging.info(`Already in ${mode} mode and playing. To restart, reset first or finish game.`);
    }

    logging.info(`Switching game mode to: ${mode}`);
    get()._stopTimerInterval(); // Stop any active timers

    set({ activeGameMode: mode, isCountdownActive: false, gameStatus: 'idle' });

    if (mode === 'timed') {
      get().startCountdown();
    } else { // 'normal'
      get()._initializeNormalModeGame();
    }
  },

  startCountdown: () => {
    logging.info("Starting countdown for Timed Mode...");
    set({ 
      gameStatus: 'countdown', 
      isCountdownActive: true, 
      countdownValue: 3,
      recentAnswers: [],
      timedScore: 0,
      score: 0,
      timer: INITIAL_TIMED_MODE_DURATION,
      lastTimeChange: null,
      timeChangeAnimationTrigger: 0,
    });
    get()._tickCountdown();
  },

  _tickCountdown: () => {
    setTimeout(() => {
      const currentCountdown = get().countdownValue;
      if (currentCountdown > 1) {
        set(state => ({ countdownValue: state.countdownValue - 1 }));
        get()._tickCountdown();
      } else {
        set({ isCountdownActive: false, countdownValue: 0 });
        logging.info("Countdown finished. Initializing Timed Mode game.");
        get()._initializeTimedModeGame();
      }
    }, 1000);
  },
  
  _initializeNormalModeGame: () => {
    logging.info("Initializing Normal Mode game...");
    set(() => ({
      currentQuestion: null,
      score: 0,
      streak: 0,
      correctCount: 0,
      totalAsked: 0,
      isAnswered: false,
      lastAnswerCorrect: null,
      gameStatus: 'idle',
      askedPlayerIds: new Set(),
      activeGameMode: 'normal',
      isCountdownActive: false,
      lastTimeChange: null,
      timeChangeAnimationTrigger: 0,
    }));
    get().nextQuestion();
    logging.info("Normal mode initialized.");
  },

  _initializeTimedModeGame: () => {
    logging.info("Initializing Timed Mode game (after countdown)...");
    set(() => ({
      currentQuestion: null,
      correctCount: 0,
      totalAsked: 0,
      isAnswered: false,
      lastAnswerCorrect: null,
      gameStatus: 'idle',
      askedPlayerIds: new Set(),
      activeGameMode: 'timed',
      timer: INITIAL_TIMED_MODE_DURATION,
      lastTimeChange: null,
      timeChangeAnimationTrigger: 0,
    }));
    get().nextQuestion();
    get()._startTimerInterval();
    logging.info("Timed mode initialized and timer started.");
  },

  nextQuestion: () => {
    const { players, askedPlayerIds, activeGameMode } = get();
    
    set({ 
      isAnswered: false,
      lastAnswerCorrect: null,
      userChoiceName: null,
      lastScoreChange: null,
      lastTimeChange: null,
    });

    const question = generateQuestion(players, askedPlayerIds);

    if (question) {
      set((state) => ({
        currentQuestion: question,
        totalAsked: state.totalAsked + 1,
        gameStatus: 'playing',
        askedPlayerIds: new Set(state.askedPlayerIds).add(question.correctPlayer.id),
      }));
    } else {
      logging.info("No more questions available or error generating.");
      set({ gameStatus: 'finished' }); 
      if (activeGameMode === 'timed') {
        get()._stopTimerInterval();
      }
    }
  },

  submitAnswer: (choiceName: string) => {
    const {
      currentQuestion,
      score,
      streak,
      bestStreak,
      correctCount,
      activeGameMode,
      timer,
      bestNormalScore,
      bestTimedScore,
      gameStatus
    } = get();

    if (!currentQuestion || get().isAnswered || gameStatus !== 'playing') {
      console.warn("[GameStore] submitAnswer called in invalid state. Ignoring.", { 
        isAnswered: get().isAnswered, 
        gameStatus 
      });
      return;
    }

    const correctChoice = currentQuestion.choices.find(c => c.isCorrect);
    const isCorrect = correctChoice?.name === choiceName;
    
    let newScore = score;
    let scoreDiff = 0;
    let newStreak = streak;
    let newBestStreak = bestStreak;
    const newCorrectCount = isCorrect ? correctCount + 1 : correctCount;
    let newTimer = timer;
    let newTimeChange = 0;

    set({ 
      isAnswered: true,
      lastAnswerCorrect: isCorrect,
      userChoiceName: choiceName
    });

    if (activeGameMode === 'normal') {
      newScore = isCorrect ? score + 10 : score;
      newStreak = isCorrect ? streak + 1 : 0;
      newBestStreak = Math.max(bestStreak, newStreak);
      scoreDiff = newScore - score;
      if (newScore > bestNormalScore) {
        set({ bestNormalScore: newScore });
        if (typeof window !== 'undefined') localStorage.setItem('bestNormalScore', newScore.toString());
      }
      const newRecentAnswer: RecentAnswer = {
        player: currentQuestion.correctPlayer,
        isCorrect: isCorrect,
        timestamp: Date.now(),
      };
      set(state => ({ recentAnswers: [newRecentAnswer, ...state.recentAnswers].slice(0, 10) }));
    } else { // Timed Mode
      newScore = isCorrect ? score + 10 : score;
      scoreDiff = newScore - score;
      if (isCorrect) {
        newTimer = Math.min(timer + 1, MAX_TIMER_CAP);
        newTimeChange = 1;
      } else {
        newTimer = Math.max(timer - 1, 0);
        newTimeChange = -1;
      }
      if (newScore > bestTimedScore) {
        set({ bestTimedScore: newScore });
        if (typeof window !== 'undefined') localStorage.setItem('bestTimedScore', newScore.toString());
      }
    }
    
    set(state => ({
      score: newScore,
      streak: newStreak,
      bestStreak: newBestStreak,
      correctCount: newCorrectCount,
      lastScoreChange: scoreDiff > 0 ? scoreDiff : null,
      animationTrigger: scoreDiff > 0 ? state.animationTrigger + 1 : state.animationTrigger,
      timer: newTimer,
      lastTimeChange: activeGameMode === 'timed' ? newTimeChange : null,
      timeChangeAnimationTrigger: activeGameMode === 'timed' && newTimeChange !== 0 ? state.timeChangeAnimationTrigger + 1 : state.timeChangeAnimationTrigger,
    }));

    if (activeGameMode === 'timed') {
      if (newTimer <= 0) {
        logging.info("Timer reached 0 due to penalty. Game Over for Timed Mode.");
        get()._stopTimerInterval();
        set({ gameStatus: 'finished' });
      } else {
        setTimeout(() => {
          if (get().gameStatus === 'playing') {
            get().nextQuestion();
          }
        }, 150);
      }
    }
  },

  resetCurrentModeGame: () => {
    logging.info(`Resetting game for mode: ${get().activeGameMode}`);
    get()._stopTimerInterval();
    if (get().activeGameMode === 'timed') {
      get().startCountdown(); // Restart timed mode with countdown
    } else {
      get()._initializeNormalModeGame(); // Restart normal mode
    }
  },

  _startTimerInterval: () => {
    get()._stopTimerInterval(); // Clear any existing timer
    logging.info("Starting timer interval for Timed Mode.");
    const intervalId = setInterval(() => {
      get()._tickTimer();
    }, 1000);
    set({ _timerIntervalId: intervalId });
  },

  _stopTimerInterval: () => {
    const intervalId = get()._timerIntervalId;
    if (intervalId) {
      logging.info("Stopping timer interval.");
      clearInterval(intervalId);
      set({ _timerIntervalId: null });
    }
  },

  _tickTimer: () => {
    const currentTimer = get().timer;
    if (currentTimer > 0) {
      set(state => ({ timer: state.timer - 1 }));
    } else {
      logging.info("Timer reached 0. Game Over for Timed Mode.");
      get()._stopTimerInterval();
      set({ gameStatus: 'finished' });
      // Update best timed score if current is higher
      if (get().score > get().bestTimedScore) {
        set(state => ({ bestTimedScore: state.score }));
        if (typeof window !== 'undefined') localStorage.setItem('bestTimedScore', get().score.toString());
      }
    }
  },
})); 