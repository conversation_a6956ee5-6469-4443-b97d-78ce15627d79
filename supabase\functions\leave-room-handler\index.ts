// supabase/functions/leave-room-handler/index.ts

// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"

import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

console.log('[EDGE_FN_LOAD] leave-room-handler function script loaded.');

interface LeaveRoomRequestBody {
  roomId?: string;
}

serve(async (req: Request) => {
  console.log(`[EDGE_LEAVE_ROOM] Request received. Method: ${req.method}, URL: ${req.url}`);
  
  if (req.method === 'OPTIONS') {
    console.log('[EDGE_LEAVE_ROOM] OPTIONS request, responding with CORS.');
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('[EDGE_LEAVE_ROOM] Processing POST request for leave-room-handler');
    
    // Environment variable checks
    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY')
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

    if (!supabaseUrl || !supabaseAnonKey || !supabaseServiceRoleKey) {
      console.error('[EDGE_LEAVE_ROOM] CRITICAL: Missing environment variables');
      throw new Error('Server configuration error: Missing Supabase credentials.')
    }
    console.log('[EDGE_LEAVE_ROOM] Environment variables loaded successfully');
    
    // Parse request body
    console.log('[EDGE_LEAVE_ROOM] Parsing request body...');
    const requestBody: LeaveRoomRequestBody = await req.json()
    console.log('[EDGE_LEAVE_ROOM] Parsed request body:', requestBody);
    const { roomId } = requestBody;

    if (!roomId) {
      console.error('[EDGE_LEAVE_ROOM] Missing roomId in request body');
      throw new Error('Room ID is required.')
    }
    console.log(`[EDGE_LEAVE_ROOM] Processing leave room request for room ID: ${roomId}`);
    
    // Authentication
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      console.error('[EDGE_LEAVE_ROOM] Missing Authorization header');
      throw new Error('User not authenticated: Missing Authorization header.')
    }
    console.log('[EDGE_LEAVE_ROOM] Authorization header present, verifying user...');

    const userClient = createClient(supabaseUrl, supabaseAnonKey, {
      global: { headers: { Authorization: authHeader } },
    })
    console.log('[EDGE_LEAVE_ROOM] User client created, getting user...');
    
    const { data: { user }, error: userError } = await userClient.auth.getUser()
    if (userError) {
      console.error('[EDGE_LEAVE_ROOM] User authentication error:', userError);
      throw new Error('User not authenticated: ' + userError.message)
    }
    if (!user) {
      console.error('[EDGE_LEAVE_ROOM] No user found in session');
      throw new Error('User not authenticated or not found.')
    }
    console.log(`[EDGE_LEAVE_ROOM] User authenticated successfully. User ID: ${user.id}`);
    const userId = user.id

    // Create admin client
    console.log('[EDGE_LEAVE_ROOM] Creating admin client...');
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey)
    console.log('[EDGE_LEAVE_ROOM] Admin client created successfully');

    console.log(`[EDGE_LEAVE_ROOM] Processing leave room request for room ${roomId} by user ${userId}`)

    // Step 1: Verify the player is in the room
    console.log(`[EDGE_LEAVE_ROOM] [VERIFICATION] Checking if user ${userId} is in room ${roomId}...`);
    const { data: existingPlayer, error: playerCheckError } = await supabaseAdmin
      .from('game_players')
      .select('user_id, room_id')
      .eq('room_id', roomId)
      .eq('user_id', userId)
      .single()

    if (playerCheckError) {
      if (playerCheckError.code === 'PGRST116') {
        console.log(`[EDGE_LEAVE_ROOM] User ${userId} not found in room ${roomId} - already left or never joined`);
        return new Response(JSON.stringify({ 
          message: 'Player was not in the room (already left or never joined).',
          roomId: roomId,
          userId: userId
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200, // This is not an error - user just wasn't in the room
        });
      } else {
        console.error(`[EDGE_LEAVE_ROOM] Database error checking player in room:`, playerCheckError);
        throw new Error('Database error: ' + playerCheckError.message)
      }
    }

    if (!existingPlayer) {
      console.log(`[EDGE_LEAVE_ROOM] User ${userId} not found in room ${roomId}`);
      return new Response(JSON.stringify({ 
        message: 'Player was not in the room.',
        roomId: roomId,
        userId: userId
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      });
    }

    console.log(`[EDGE_LEAVE_ROOM] ✅ User ${userId} confirmed in room ${roomId}`);

    // Step 2: Check room status to determine leave behavior
    console.log(`[EDGE_LEAVE_ROOM] [ROOM_STATUS_CHECK] Checking room status to determine leave behavior...`);
    const { data: roomData, error: roomFetchError } = await supabaseAdmin
      .from('game_rooms')
      .select('id, status, host_id, title, created_at')
      .eq('id', roomId)
      .single();

    if (roomFetchError) {
      console.error(`[EDGE_LEAVE_ROOM] Error fetching room data:`, roomFetchError);
      throw new Error(`Failed to check room status: ${roomFetchError.message}`)
    }

    if (!roomData) {
      console.error(`[EDGE_LEAVE_ROOM] Room ${roomId} not found`);
      throw new Error(`Room not found: ${roomId}`)
    }

    console.log(`[EDGE_LEAVE_ROOM] Room ${roomId} status: ${roomData.status}`);

    let remainingPlayersCount: number;
    let playerWasDeleted = false;

    // Step 3: Handle player removal based on room status
    if (roomData.status === 'active') {
      // For ACTIVE games: Mark player as disconnected (allow reconnection)
      console.log(`[EDGE_LEAVE_ROOM] [DISCONNECT_PLAYER] Room is ACTIVE - marking player as disconnected for potential reconnection...`);

      const { error: disconnectError } = await supabaseAdmin
        .from('game_players')
        .update({
          is_connected: false,
          last_seen_at: new Date().toISOString()
        })
        .match({ room_id: roomId, user_id: userId });

      if (disconnectError) {
        console.error(`[EDGE_LEAVE_ROOM] Database error marking player as disconnected:`, disconnectError);
        throw new Error(`Failed to disconnect from active game: ${disconnectError.message}`)
      }

      console.log(`[EDGE_LEAVE_ROOM] ✅ Successfully marked user ${userId} as disconnected from active room ${roomId}`);

      // Count connected players only for active games
      const { count: connectedCount, error: countError } = await supabaseAdmin
        .from('game_players')
        .select('*', { count: 'exact', head: true })
        .eq('room_id', roomId)
        .eq('is_connected', true);

      if (countError) {
        console.error(`[EDGE_LEAVE_ROOM] Error counting connected players:`, countError);
        throw new Error(`Error checking room state: ${countError.message}`)
      }

      remainingPlayersCount = connectedCount || 0;
      console.log(`[EDGE_LEAVE_ROOM] Active room ${roomId} now has ${remainingPlayersCount} connected players`);

    } else {
      // For WAITING/FINISHED games: Delete player completely
      console.log(`[EDGE_LEAVE_ROOM] [DELETE_PLAYER] Room is ${roomData.status.toUpperCase()} - removing player completely...`);

      const { error: deletePlayerError } = await supabaseAdmin
        .from('game_players')
        .delete()
        .match({ room_id: roomId, user_id: userId });

      if (deletePlayerError) {
        console.error(`[EDGE_LEAVE_ROOM] Database error removing player from room:`, deletePlayerError);
        throw new Error(`Failed to leave room: ${deletePlayerError.message}`)
      }

      console.log(`[EDGE_LEAVE_ROOM] ✅ Successfully removed user ${userId} from ${roomData.status} room ${roomId}`);
      playerWasDeleted = true;

      // Count all remaining players for waiting/finished games
      const { count: totalCount, error: countError } = await supabaseAdmin
        .from('game_players')
        .select('*', { count: 'exact', head: true })
        .eq('room_id', roomId);

      if (countError) {
        console.error(`[EDGE_LEAVE_ROOM] Error counting remaining players:`, countError);
        throw new Error(`Error checking room state: ${countError.message}`)
      }

      remainingPlayersCount = totalCount || 0;
      console.log(`[EDGE_LEAVE_ROOM] ${roomData.status} room ${roomId} now has ${remainingPlayersCount} total players`);
    }

    // Step 4: Handle room cleanup based on player count and room status
    console.log(`[EDGE_LEAVE_ROOM] [CLEANUP_CHECK] Checking if room cleanup is needed...`);

    if (remainingPlayersCount === 0) {
      if (roomData.status === 'active') {
        // Active room with no connected players - keep room but log the situation
        console.log(`[EDGE_LEAVE_ROOM] ℹ️ Active room ${roomId} has no connected players but keeping room for potential reconnections`);
      } else {
        // Waiting/finished room with no players - delete it
        console.log(`[EDGE_LEAVE_ROOM] [ROOM_DELETION] ${roomData.status} room ${roomId} is now empty. Deleting room...`);

        // Delete room if it's not 'completed' (we might want to keep completed games for a while)
        if (roomData.status !== 'completed') {
          const { error: deleteRoomError } = await supabaseAdmin
            .from('game_rooms')
            .delete()
            .eq('id', roomId);

          if (deleteRoomError) {
            console.error(`[EDGE_LEAVE_ROOM] Error deleting empty room ${roomId}:`, deleteRoomError);
            // Don't throw error here - player was successfully removed
            console.log(`[EDGE_LEAVE_ROOM] ⚠️ Could not delete empty room, but player removal was successful`);
          } else {
            console.log(`[EDGE_LEAVE_ROOM] ✅ Successfully deleted empty ${roomData.status} room ${roomId}`);
          }
        } else {
          console.log(`[EDGE_LEAVE_ROOM] ℹ️ Room ${roomId} is completed - keeping for history`);
        }
      }
    } else {
      const playerType = roomData.status === 'active' ? 'connected players' : 'total players';
      console.log(`[EDGE_LEAVE_ROOM] ℹ️ Room ${roomId} still has ${remainingPlayersCount} ${playerType}, keeping room`);
    }

    const actionTaken = roomData.status === 'active' ? 'disconnected' : 'removed';
    console.log(`[EDGE_LEAVE_ROOM] SUCCESS: User ${userId} successfully ${actionTaken} from room ${roomId}`)

    // Return success with comprehensive data
    return new Response(JSON.stringify({
      message: `Successfully ${actionTaken} from the room!`,
      roomId: roomId,
      userId: userId,
      roomStatus: roomData.status,
      actionTaken: actionTaken,
      remainingPlayersCount: remainingPlayersCount,
      playerWasDeleted: playerWasDeleted,
      roomDeleted: remainingPlayersCount === 0 && roomData.status !== 'active' && roomData.status !== 'completed',
      canReconnect: roomData.status === 'active' && !playerWasDeleted,
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })

  } catch (error) {
    console.error('[EDGE_LEAVE_ROOM] UNHANDLED EXCEPTION in leave-room-handler:', error);
    if (error instanceof Error) {
      console.error('[EDGE_LEAVE_ROOM] Exception Name:', error.name);
      console.error('[EDGE_LEAVE_ROOM] Exception Message:', error.message);
      console.error('[EDGE_LEAVE_ROOM] Exception Stack:', error.stack);
    }
    
    // Determine appropriate status code based on error type/message
    let statusCode = 500; // Default to internal server error
    let errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    
    if (errorMessage.includes('Room ID is required') || errorMessage.includes('Missing roomId')) {
      statusCode = 400; // Bad Request for missing required fields
    } else if (errorMessage.includes('not authenticated') || errorMessage.includes('Missing Authorization')) {
      statusCode = 401; // Unauthorized for authentication issues
    } else if (errorMessage.includes('Database error')) {
      statusCode = 500; // Keep as 500 for actual database issues
      errorMessage = 'Database error occurred while processing request';
    } else if (errorMessage.includes('Server configuration error')) {
      statusCode = 503; // Service Unavailable for configuration issues
      errorMessage = 'Service temporarily unavailable due to configuration issues';
    }
    
    console.error(`[EDGE_LEAVE_ROOM] Returning ${statusCode} status with message: ${errorMessage}`);
    
    return new Response(JSON.stringify({ 
      error: errorMessage, 
      details: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: statusCode,
    })
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/leave-room-handler' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"roomId":"your-room-id-here"}'

*/