# State Synchronization Bug Fixes

## Problem Summary

The application had a critical state synchronization bug where the host's <PERSON><PERSON> would remain in the "waiting room" view even after successfully starting a game on the backend. This was caused by:

1. **Tab Switch Race Condition**: When users switched tabs immediately after clicking "Start Game", the WebSocket connection was suspended before the real-time update confirming `status: 'active'` could be processed.

2. **Incomplete Recovery Logic**: The visibility change handler only fetched player data but not the room data containing the critical `status` field.

3. **Stale State**: The client's `activeRoomData`/`currentRoomGameData` state remained stale with `status: 'waiting'`, causing the UI to show the lobby instead of the game view.

## Root Cause Analysis

From the detailed log analysis:
- Game successfully started on backend (room status changed to `active`)
- User switched tabs immediately after clicking "Start Game"
- WebSocket connection suspended by browser throttling
- Recovery logic only refreshed player list, not room status
- <PERSON><PERSON> remained stuck in lobby view with stale `status: 'waiting'`
- Subsequent "Start Game" clicks correctly failed pre-flight checks but didn't update UI

## Implemented Fixes

### 1. Enhanced Visibility Change Handler (`web-app/src/app/page.tsx`)

**Location**: Lines 693-735 in `handleVisibilityChange` function

**Changes**:
- Added comprehensive room data fetching alongside player data
- Updates both `currentRoomGameData` and `gameRooms` array with fresh server state
- Provides proper error handling and user feedback
- Ensures complete state synchronization on tab recovery

```typescript
// CRITICAL FIX: RE-FETCH ALL RELEVANT ROOM DATA
const { data: roomData, error: roomError } = await supabase
  .from('game_rooms')
  .select('*')
  .eq('id', activeRoomId)
  .single();

if (roomError || !roomData) {
  console.error('[DISCONNECT_DETECTION] Failed to re-fetch room data on reconnect', roomError);
  setErrorMp('Failed to reconnect to room. Please try rejoining.');
  return;
}

// Update the state with the fresh data from the server
setCurrentRoomGameData(roomData);
setGameRooms(prevRooms =>
  prevRooms.map(room => room.id === activeRoomId ? { ...room, ...roomData } : room)
);
```

### 2. Smart State Synchronization in handleStartGame

**Location**: 
- `web-app/src/app/page.tsx` lines 2978-3057
- `src/app/page.tsx` lines 567-642

**Changes**:
- Fetches fresh room data from server instead of relying on potentially stale `gameRooms` array
- Detects when game has already started and forces UI state synchronization
- Prevents duplicate start attempts while gracefully handling race conditions

```typescript
// CRITICAL FIX: Handle case where game has already started (state synchronization)
if (currentRoomDetails.status !== 'waiting') {
  console.warn(`[Client] [STATE_SYNC_FIX] Attempted to start a game that is not in 'waiting' status (current: ${currentRoomDetails.status}). Forcing state synchronization.`);
  
  // The game is already active. The client is out of sync.
  // Force the client to catch up to the correct state.
  setCurrentRoomGameData(currentRoomDetails); // Update state with the correct room data
  
  // Also update the gameRooms array to keep it in sync
  setGameRooms(prevRooms =>
    prevRooms.map(room => room.id === activeRoomId ? { ...room, ...currentRoomDetails } : room)
  );
  
  // The UI will now re-render based on the correct status
  console.log(`[Client] [STATE_SYNC_FIX] State synchronization complete. UI should now reflect status: ${currentRoomDetails.status}`);
  
  setIsStartingGame(false); // Clear loading state
  return; // Stop function execution
}
```

## Technical Benefits

1. **Robust Recovery**: Complete state resynchronization on tab visibility changes
2. **Race Condition Prevention**: Handles concurrent start attempts gracefully  
3. **User Experience**: No more stuck states - UI always reflects actual game status
4. **Debugging**: Enhanced logging for easier troubleshooting
5. **Data Consistency**: Both local state objects stay in sync with server

## Testing Recommendations

1. **Tab Switch Test**: Start game, immediately switch tabs, switch back - UI should show game view
2. **Concurrent Start Test**: Multiple rapid "Start Game" clicks should be handled gracefully
3. **Network Recovery Test**: Disconnect/reconnect during game start should recover properly
4. **State Consistency Test**: Verify `currentRoomGameData` and `gameRooms` stay synchronized

## Files Modified

- `web-app/src/app/page.tsx`: Enhanced visibility handler and handleStartGame
- `src/app/page.tsx`: Enhanced handleStartGame with state sync logic

## Next Steps

Consider implementing the architectural improvements suggested:
1. Break down the monolithic `page.tsx` into smaller, focused components
2. Create custom hooks for auth, lobby, and room management
3. Implement proper state machines for connection status
4. Add more granular real-time subscription management
