# Connection Recovery Test Guide

## Testing the "Stuck in Reconnecting State" Fix

### **What We Fixed**
1. **Connection Status Recovery**: Connection status now properly transitions from `RECONNECTING` back to `CONNECTED` after successful recovery
2. **Resilient UI Buttons**: Start Game and Ready buttons remain available during reconnection (only disabled when completely offline)
3. **Recovery Timeout**: Prevents infinite reconnecting state with 30-second timeout
4. **Enhanced Recovery Detection**: Better logic to detect when recovery attempts succeed

### **Manual Test Steps**

#### **Step 1: Setup Test Environment**
1. Open the application at http://localhost:3001
2. Sign in as user "fresh"
3. Create a new multiplayer room
4. Note the room ID from the URL or logs

#### **Step 2: Simulate Connection Failure**
1. Open browser Developer Tools (F12)
2. Go to Network tab
3. Find the WebSocket connection to Supabase (look for `wss://` URLs)
4. Right-click on the WebSocket connection and select "Block request URL" or use throttling to simulate network issues

#### **Step 3: Observe Recovery Behavior**
1. Watch the connection status indicator in the UI
2. Should show "Reconnecting..." with spinner
3. Start Game button should remain visible (not disappear)
4. <PERSON><PERSON> should show helpful tooltip: "Connection is recovering. You can still start the game."

#### **Step 4: Verify Recovery**
1. Unblock the network or restore connection
2. Connection status should return to normal (indicator disappears)
3. Start Game button should become fully functional
4. Check browser console for recovery success logs

### **Expected Log Messages**

#### **During Connection Failure:**
```
[Realtime] ❌ [PLAYERS_SUBSCRIPTION_ERROR] game_players channel error/timeout for ACTIVE room-...
[Realtime] ⚠️ Realtime connection interrupted. Attempting to recover.
[Realtime] [RECOVERY_ATTEMPT] Attempting to recover game_players subscription for room-...
```

#### **During Successful Recovery:**
```
[Realtime] [RECOVERY_SUCCESS] Successfully recovered game_players data for room-...
[Realtime] ✅ Successfully subscribed to game_players for room-...
```

#### **If Recovery Fails (after 30 seconds):**
```
[Realtime] [RECOVERY_TIMEOUT] Setting connection to OFFLINE after failed recovery attempts
```

### **UI Behavior Verification**

#### **Before Fix (Old Behavior):**
- ❌ Start Game button disappears during reconnection
- ❌ Connection gets stuck in "Reconnecting" state
- ❌ No way to start game even if data is available

#### **After Fix (New Behavior):**
- ✅ Start Game button remains visible during reconnection
- ✅ Connection status properly transitions back to connected
- ✅ Helpful tooltips explain current state
- ✅ Timeout prevents infinite reconnecting state

### **Advanced Testing**

#### **Test Connection Status State Machine:**
1. **INITIALIZING** → **CONNECTED**: When joining room and subscriptions succeed
2. **CONNECTED** → **RECONNECTING**: When subscription errors occur
3. **RECONNECTING** → **CONNECTED**: When recovery succeeds
4. **RECONNECTING** → **OFFLINE**: When recovery times out (30 seconds)

#### **Test Button States:**
- **Start Game Button**: Only disabled when `OFFLINE` or `!isOnline`
- **Ready Button**: Only disabled when `OFFLINE` or `!isOnline`
- **Create Room Button**: Disabled when `!isOnline`

### **Troubleshooting**

#### **If Recovery Still Fails:**
1. Check Supabase RLS policies for `game_players` table
2. Verify realtime publication includes all events (insert, update, delete)
3. Check browser console for specific error messages
4. Verify user authentication is still valid

#### **If Buttons Still Disappear:**
1. Check if `connectionStatus` is being set correctly
2. Verify button logic uses `=== 'OFFLINE'` not `!== 'CONNECTED'`
3. Check for any other conditions that might hide buttons

### **Success Criteria**
- ✅ Connection status indicator shows appropriate states
- ✅ Start Game button remains available during reconnection
- ✅ Recovery completes successfully and status returns to normal
- ✅ No infinite reconnecting loops
- ✅ Helpful user feedback through tooltips and status messages
