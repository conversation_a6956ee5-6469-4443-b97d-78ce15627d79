# Multiplayer Reconnection Testing Guide

## 🧪 **Manual Testing Procedures**

### **Prerequisites**
1. Deploy the updated edge functions:
   ```bash
   supabase functions deploy leave-room-handler
   supabase functions deploy heartbeat-handler
   ```

2. Apply database migrations:
   ```bash
   supabase db push
   ```

3. Have multiple browser windows/devices ready for testing

---

## **Test Scenario 1: Basic Player Reconnection**

### **Setup**
1. Player A creates a room
2. Player B joins the room
3. Both players mark ready
4. Host starts the game (room status becomes 'active')

### **Test Steps**
1. **Player B closes browser tab** (simulates disconnect)
2. **Verify in database**: Player B should be marked as `is_connected: false`
3. **Player B reopens browser and navigates back to app**
4. **Player <PERSON> clicks "Join Room" with same room code**
5. **Expected Result**: Player B should rejoin the same game session

### **Validation Points**
- [ ] Player B appears in player list as reconnected
- [ ] Player B retains their original position/identity
- [ ] Game state is preserved (current question, scores, etc.)
- [ ] No duplicate players in the room
- [ ] Realtime updates work correctly for both players

---

## **Test Scenario 2: Host Reconnection**

### **Setup**
1. Player A (host) creates a room
2. Player B joins the room
3. Start an active game

### **Test Steps**
1. **Host (Player A) closes browser tab**
2. **Verify**: Player A marked as `is_connected: false` but still host
3. **Player A reopens browser and rejoins**
4. **Expected Result**: Player A should rejoin as host

### **Validation Points**
- [ ] Player A retains host privileges
- [ ] Game continues normally
- [ ] Host controls (start game, etc.) work correctly
- [ ] Other players see host as reconnected

---

## **Test Scenario 3: Network Disconnection**

### **Setup**
1. Two players in an active game
2. Use browser dev tools to simulate network issues

### **Test Steps**
1. **Open browser dev tools → Network tab**
2. **Set network to "Offline" for Player B**
3. **Wait 30 seconds** (heartbeat should fail)
4. **Re-enable network**
5. **Player B should attempt to rejoin**

### **Validation Points**
- [ ] Player B automatically marked as disconnected during offline period
- [ ] Player B can successfully rejoin when network restored
- [ ] Heartbeat system resumes correctly
- [ ] No connection errors in console

---

## **Test Scenario 4: Multiple Simultaneous Disconnections**

### **Setup**
1. 3-4 players in an active game

### **Test Steps**
1. **Multiple players close browsers simultaneously**
2. **Verify database state**: All should be marked as disconnected
3. **Players rejoin in different order**
4. **Expected Result**: All players should be able to rejoin

### **Validation Points**
- [ ] Room remains active with 0 connected players
- [ ] Players can rejoin in any order
- [ ] Game state preserved throughout
- [ ] No race conditions or conflicts

---

## **Test Scenario 5: Heartbeat System**

### **Setup**
1. Player in an active room
2. Monitor browser console for heartbeat logs

### **Test Steps**
1. **Join a room and observe console logs**
2. **Look for heartbeat ping messages every 30 seconds**
3. **Close browser tab abruptly** (don't use leave room button)
4. **Wait 5+ minutes**
5. **Check database**: Player should be marked as disconnected**

### **Validation Points**
- [ ] Heartbeat pings sent every 30 seconds
- [ ] `last_seen_at` timestamp updated with each ping
- [ ] Stale connections cleaned up after 5 minutes
- [ ] No heartbeat errors in logs

---

## **Test Scenario 6: Leave vs Disconnect Behavior**

### **Setup**
1. Two separate rooms: one waiting, one active

### **Test Steps**
1. **Waiting Room**: Player joins, then clicks "Leave Room"
2. **Active Room**: Player joins active game, then clicks "Leave Room"
3. **Try to rejoin both rooms**

### **Expected Results**
- **Waiting Room**: Player should be completely removed, cannot rejoin
- **Active Room**: Player should be marked as disconnected, can rejoin

### **Validation Points**
- [ ] Different behavior for waiting vs active rooms
- [ ] Waiting room players deleted completely
- [ ] Active room players marked as disconnected
- [ ] Reconnection only works for active rooms

---

## **Database Queries for Validation**

### **Check Player Connection Status**
```sql
SELECT 
    gp.user_id,
    p.username,
    gp.room_id,
    gp.is_connected,
    gp.last_seen_at,
    gr.status as room_status
FROM game_players gp
JOIN profiles p ON gp.user_id = p.id
JOIN game_rooms gr ON gp.room_id = gr.id
WHERE gp.room_id = 'YOUR_ROOM_ID'
ORDER BY gp.created_at;
```

### **Check Room State**
```sql
SELECT 
    id,
    status,
    host_id,
    title,
    (SELECT COUNT(*) FROM game_players WHERE room_id = gr.id) as total_players,
    (SELECT COUNT(*) FROM game_players WHERE room_id = gr.id AND is_connected = true) as connected_players
FROM game_rooms gr
WHERE id = 'YOUR_ROOM_ID';
```

### **Find Stale Connections**
```sql
SELECT 
    gp.user_id,
    p.username,
    gp.room_id,
    gp.is_connected,
    gp.last_seen_at,
    NOW() - gp.last_seen_at as time_since_last_seen
FROM game_players gp
JOIN profiles p ON gp.user_id = p.id
WHERE gp.is_connected = true 
AND gp.last_seen_at < NOW() - INTERVAL '5 minutes'
ORDER BY gp.last_seen_at;
```

---

## **Common Issues to Watch For**

### **Connection Issues**
- Heartbeat failures due to network problems
- Realtime subscription disconnections
- Edge function timeout errors

### **State Inconsistencies**
- Players appearing twice in room
- Incorrect `is_connected` status
- Missing `last_seen_at` updates

### **UI Problems**
- Player list not updating after reconnection
- Host controls not working after reconnection
- Error messages not clearing properly

### **Performance Issues**
- Slow reconnection times
- Database query timeouts
- Memory leaks from event listeners

---

## **Success Criteria**

✅ **All reconnection scenarios work reliably**
✅ **No duplicate players or data corruption**
✅ **Heartbeat system maintains connection health**
✅ **Performance remains good with multiple players**
✅ **Error handling provides clear user feedback**
✅ **Database state remains consistent**

---

*Use this guide to systematically test all reconnection functionality before deploying to production.*
