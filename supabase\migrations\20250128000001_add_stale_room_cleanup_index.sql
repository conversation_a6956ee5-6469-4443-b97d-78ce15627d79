-- Migration: Add index for efficient stale room cleanup
-- Created: 2025-01-28
-- Purpose: Optimize the stale-room-janitor Edge Function queries

-- Add index on last_activity_timestamp for efficient stale room queries
-- This index will be used by the janitor function to quickly find rooms that need cleanup
CREATE INDEX IF NOT EXISTS idx_game_rooms_stale_cleanup 
ON game_rooms (status, last_activity_timestamp);

-- Add comment to document the index purpose
COMMENT ON INDEX idx_game_rooms_stale_cleanup IS 'Composite index for efficient stale room cleanup queries by janitor function';

-- Ensure last_activity_timestamp is updated when rooms are modified
-- Create or replace function to automatically update last_activity_timestamp
CREATE OR REPLACE FUNCTION update_room_activity_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    -- Only update last_activity_timestamp if it's not explicitly being set in the UPDATE
    -- This prevents overriding intentional timestamp updates
    IF TG_OP = 'UPDATE' AND OLD.last_activity_timestamp = NEW.last_activity_timestamp THEN
        NEW.last_activity_timestamp = NOW();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON><PERSON> trigger to automatically update last_activity_timestamp on game_rooms updates
-- This trigger will fire before any UPDATE on game_rooms table
DROP TRIGGER IF EXISTS trigger_update_room_activity_timestamp ON game_rooms;
CREATE TRIGGER trigger_update_room_activity_timestamp
    BEFORE UPDATE ON game_rooms
    FOR EACH ROW
    EXECUTE FUNCTION update_room_activity_timestamp();

-- Add comments for documentation
COMMENT ON FUNCTION update_room_activity_timestamp() IS 'Automatically updates last_activity_timestamp on game_rooms updates';
COMMENT ON TRIGGER trigger_update_room_activity_timestamp ON game_rooms IS 'Trigger to maintain last_activity_timestamp for stale room detection';
