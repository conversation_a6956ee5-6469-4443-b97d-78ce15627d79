// supabase/functions/heartbeat-handler/index.ts

import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'

console.log('[EDGE_FN_LOAD] heartbeat-handler function script loaded.');

interface HeartbeatRequestBody {
  roomId?: string;
  action?: 'ping' | 'cleanup_stale';
}

serve(async (req: Request) => {
  console.log(`[EDGE_HEARTBEAT] Request received. Method: ${req.method}, URL: ${req.url}`);
  
  if (req.method === 'OPTIONS') {
    console.log('[EDGE_HEARTBEAT] OPTIONS request, responding with CORS.');
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    console.log('[EDGE_HEARTBEAT] Processing POST request for heartbeat-handler');
    
    // Environment variable checks
    const supabaseUrl = Deno.env.get('SUPABASE_URL')
    const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY')
    const supabaseServiceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')

    if (!supabaseUrl || !supabaseAnonKey || !supabaseServiceRoleKey) {
      console.error('[EDGE_HEARTBEAT] CRITICAL: Missing environment variables');
      throw new Error('Server configuration error: Missing Supabase credentials.')
    }
    console.log('[EDGE_HEARTBEAT] Environment variables loaded successfully');
    
    // Parse request body
    console.log('[EDGE_HEARTBEAT] Parsing request body...');
    const requestBody: HeartbeatRequestBody = await req.json()
    console.log('[EDGE_HEARTBEAT] Parsed request body:', requestBody);
    const { roomId, action = 'ping' } = requestBody;
    
    // Authentication
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      console.error('[EDGE_HEARTBEAT] Missing Authorization header');
      throw new Error('User not authenticated: Missing Authorization header.')
    }
    console.log('[EDGE_HEARTBEAT] Authorization header present, verifying user...');

    const userClient = createClient(supabaseUrl, supabaseAnonKey, {
      global: { headers: { Authorization: authHeader } },
    })
    console.log('[EDGE_HEARTBEAT] User client created, getting user...');
    
    const { data: { user }, error: userError } = await userClient.auth.getUser()
    if (userError) {
      console.error('[EDGE_HEARTBEAT] User authentication error:', userError);
      throw new Error('User not authenticated: ' + userError.message)
    }
    if (!user) {
      console.error('[EDGE_HEARTBEAT] No user found in session');
      throw new Error('User not authenticated or not found.')
    }
    console.log(`[EDGE_HEARTBEAT] User authenticated successfully. User ID: ${user.id}`);
    const userId = user.id

    // Create admin client
    console.log('[EDGE_HEARTBEAT] Creating admin client...');
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceRoleKey)
    console.log('[EDGE_HEARTBEAT] Admin client created successfully');

    if (action === 'ping' && roomId) {
      // Handle heartbeat ping - update last_seen_at for user in specific room
      console.log(`[EDGE_HEARTBEAT] [PING] Processing heartbeat ping for user ${userId} in room ${roomId}`);
      
      const { error: updateError } = await supabaseAdmin
        .from('game_players')
        .update({ 
          last_seen_at: new Date().toISOString(),
          is_connected: true // Ensure they're marked as connected
        })
        .eq('room_id', roomId)
        .eq('user_id', userId);

      if (updateError) {
        console.error(`[EDGE_HEARTBEAT] Error updating heartbeat for user ${userId} in room ${roomId}:`, updateError);
        throw new Error(`Failed to update heartbeat: ${updateError.message}`)
      }

      console.log(`[EDGE_HEARTBEAT] ✅ Successfully updated heartbeat for user ${userId} in room ${roomId}`);
      
      return new Response(JSON.stringify({ 
        message: 'Heartbeat updated successfully',
        userId: userId,
        roomId: roomId,
        timestamp: new Date().toISOString()
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      })

    } else if (action === 'cleanup_stale') {
      // Handle cleanup of stale connections (players who haven't been seen for a while)
      console.log(`[EDGE_HEARTBEAT] [CLEANUP] Processing cleanup of stale connections`);
      
      // Define stale threshold (e.g., 5 minutes)
      const staleThresholdMinutes = 5;
      const staleThreshold = new Date(Date.now() - staleThresholdMinutes * 60 * 1000).toISOString();
      
      console.log(`[EDGE_HEARTBEAT] [CLEANUP] Looking for players with last_seen_at before ${staleThreshold}`);
      
      // Find stale connected players
      const { data: stalePlayers, error: staleError } = await supabaseAdmin
        .from('game_players')
        .select('user_id, room_id, last_seen_at, profiles:user_id(username)')
        .eq('is_connected', true)
        .lt('last_seen_at', staleThreshold);

      if (staleError) {
        console.error(`[EDGE_HEARTBEAT] Error finding stale players:`, staleError);
        throw new Error(`Failed to find stale players: ${staleError.message}`)
      }

      console.log(`[EDGE_HEARTBEAT] [CLEANUP] Found ${stalePlayers?.length || 0} stale players`);
      
      if (stalePlayers && stalePlayers.length > 0) {
        // Mark stale players as disconnected
        const stalePlayerIds = stalePlayers.map(p => p.user_id);
        
        const { error: disconnectError } = await supabaseAdmin
          .from('game_players')
          .update({ is_connected: false })
          .in('user_id', stalePlayerIds)
          .eq('is_connected', true);

        if (disconnectError) {
          console.error(`[EDGE_HEARTBEAT] Error marking stale players as disconnected:`, disconnectError);
          throw new Error(`Failed to disconnect stale players: ${disconnectError.message}`)
        }

        console.log(`[EDGE_HEARTBEAT] ✅ Successfully marked ${stalePlayers.length} stale players as disconnected`);
        
        return new Response(JSON.stringify({ 
          message: 'Stale connections cleaned up successfully',
          stalePlayersCount: stalePlayers.length,
          stalePlayerIds: stalePlayerIds,
          threshold: staleThreshold,
          timestamp: new Date().toISOString()
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      } else {
        console.log(`[EDGE_HEARTBEAT] [CLEANUP] No stale players found`);
        
        return new Response(JSON.stringify({ 
          message: 'No stale connections found',
          stalePlayersCount: 0,
          threshold: staleThreshold,
          timestamp: new Date().toISOString()
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      }

    } else {
      console.error('[EDGE_HEARTBEAT] Invalid action or missing roomId');
      throw new Error('Invalid action or missing roomId. Use action="ping" with roomId or action="cleanup_stale".')
    }

  } catch (error) {
    console.error('[EDGE_HEARTBEAT] UNHANDLED EXCEPTION in heartbeat-handler:', error);
    if (error instanceof Error) {
      console.error('[EDGE_HEARTBEAT] Exception Name:', error.name);
      console.error('[EDGE_HEARTBEAT] Exception Message:', error.message);
      console.error('[EDGE_HEARTBEAT] Exception Stack:', error.stack);
    }
    
    // Determine appropriate status code based on error type/message
    let statusCode = 500; // Default to internal server error
    let errorMessage = error instanceof Error ? error.message : 'Internal Server Error';
    
    if (errorMessage.includes('Missing roomId') || errorMessage.includes('Invalid action')) {
      statusCode = 400; // Bad Request for missing required fields
    } else if (errorMessage.includes('not authenticated') || errorMessage.includes('Missing Authorization')) {
      statusCode = 401; // Unauthorized for authentication issues
    } else if (errorMessage.includes('Database error')) {
      statusCode = 500; // Keep as 500 for actual database issues
      errorMessage = 'Database error occurred while processing request';
    } else if (errorMessage.includes('Server configuration error')) {
      statusCode = 503; // Service Unavailable for configuration issues
      errorMessage = 'Service temporarily unavailable due to configuration issues';
    }
    
    console.error(`[EDGE_HEARTBEAT] Returning ${statusCode} status with message: ${errorMessage}`);
    
    return new Response(JSON.stringify({ 
      error: errorMessage, 
      details: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: statusCode,
    })
  }
})

/* To invoke locally:

  1. Run `supabase start` (see: https://supabase.com/docs/reference/cli/supabase-start)
  2. Make an HTTP request:

  curl -i --location --request POST 'http://127.0.0.1:54321/functions/v1/heartbeat-handler' \
    --header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0' \
    --header 'Content-Type: application/json' \
    --data '{"roomId":"your-room-id-here","action":"ping"}'

*/
