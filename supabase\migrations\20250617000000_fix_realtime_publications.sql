-- Fix Realtime Publications for Global Subscriptions
-- This migration ensures that game_rooms and game_players tables are properly configured
-- for realtime subscriptions to prevent "Unknown error" on global subscriptions

-- Enable realtime for game_rooms table
ALTER PUBLICATION supabase_realtime ADD TABLE game_rooms;

-- Enable realtime for game_players table  
ALTER PUBLICATION supabase_realtime ADD TABLE game_players;

-- Ensure the tables have proper RLS policies for realtime access
-- (The existing RLS policies should already handle this, but we're being explicit)

-- Verify that authenticated users can read game_rooms for realtime subscriptions
-- This policy should already exist, but we'll ensure it's properly configured
DO $$
BEGIN
    -- Check if the policy exists
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'game_rooms' 
        AND policyname = 'Users can view all game rooms'
    ) THEN
        -- Create the policy if it doesn't exist
        CREATE POLICY "Users can view all game rooms" ON "public"."game_rooms"
        AS PERMISSIVE FOR SELECT
        TO authenticated
        USING (true);
    END IF;
END $$;

-- Verify that authenticated users can read game_players for realtime subscriptions
DO $$
BEGIN
    -- Check if the policy exists
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'game_players' 
        AND policyname = 'Users can view all game players'
    ) THEN
        -- Create the policy if it doesn't exist
        CREATE POLICY "Users can view all game players" ON "public"."game_players"
        AS PERMISSIVE FOR SELECT
        TO authenticated
        USING (true);
    END IF;
END $$;

-- Grant necessary permissions for realtime subscriptions
GRANT SELECT ON game_rooms TO authenticated;
GRANT SELECT ON game_players TO authenticated;

-- Ensure the realtime schema has access to these tables
GRANT USAGE ON SCHEMA public TO supabase_realtime_admin;
GRANT SELECT ON game_rooms TO supabase_realtime_admin;
GRANT SELECT ON game_players TO supabase_realtime_admin;
