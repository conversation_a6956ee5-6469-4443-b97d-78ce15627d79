// FootballFx.tsx
import { motion, AnimatePresence } from "framer-motion";
import { useMemo } from "react";

interface Props {
  /** Every time this number increments (> 0), a new burst plays. */
  trigger: number | null;
  /** Current streak of correct answers */
  streak: number;
}

interface FootballCfg {
  initScale: number;
  initX: number;
  angle: number;
  distance: number;
  duration: number;
  delay: number;
  spin: number;
  stretch: number;
}

const MAX_FOOTBALLS = 5;

export function FootballFx({ trigger, streak }: Props) {
  /** Pre-baked random config for each football in the current burst. */
  const configs = useMemo<FootballCfg[]>(() => {
    if (!trigger) return [];

    // Use streak count for number of footballs, capped at MAX_FOOTBALLS
    const count = Math.min(streak, MAX_FOOTBALLS);

    return Array.from({ length: count }, (_, i) => {
      const initScale = 0.9 + Math.random() * 0.2;          // bigger start
      const initX = (Math.random() - 0.5) * 40;

      const angle =
        -Math.PI / 2 + (Math.random() - 0.5) * (Math.PI / 4); // ±22.5°
      const distance = 150 + Math.random() * 80;

      return {
        initScale,
        initX,
        angle,
        distance,
        duration: 0.8 + Math.random() * 0.4,
        delay: i * 0.06,
        spin: (Math.random() - 0.5) * 120,                    // degrees
        stretch: 1.35 + Math.random() * 0.1,                  // juicy overshoot
      };
    });
  }, [streak, trigger]);

  if (!trigger) return null;

  return (
    <AnimatePresence>
      <div
        className="absolute inset-0 flex items-center justify-center pointer-events-none z-40 no-debug-box"
        style={{ transform: "translateY(-150px)", overflow: "visible" }}
      >
        {configs.map((cfg, i) => {
          const targetX = cfg.initX + Math.cos(cfg.angle) * cfg.distance * 0.35;
          const targetY = Math.sin(cfg.angle) * cfg.distance;

          return (
            <motion.span
              key={`football-${trigger}-${i}`}
              initial={{
                opacity: 1,
                scale: cfg.initScale,
                x: cfg.initX,
                y: 0,
                rotate: 0,
              }}
              animate={{
                opacity: [1, 1, 0],
                scale: [cfg.initScale, cfg.stretch, cfg.stretch * 0.95],
                x: [cfg.initX, targetX],
                y: [0, targetY],
                rotate: [0, cfg.spin],
              }}
              transition={{
                duration: cfg.duration,
                delay: cfg.delay,
                ease: "easeOut",
                opacity: {
                  duration: cfg.duration * 0.35,
                  delay: cfg.delay + cfg.duration * 0.65,
                },
              }}
              exit={{ opacity: 0 }}
              className="text-4xl md:text-5xl select-none"
              style={{
                textShadow: "1px 1px 3px rgba(0,0,0,0.6)",
                willChange: "transform, opacity",
                transformOrigin: "center",
                transform: "translateZ(0)", // GPU promotion
              }}
            >
              🏈
            </motion.span>
          );
        })}
      </div>
    </AnimatePresence>
  );
}
