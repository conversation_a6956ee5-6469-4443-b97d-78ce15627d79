import { motion, AnimatePresence } from 'framer-motion';
import { useEffect, useMemo, useState } from 'react';
import { createPortal } from 'react-dom';

interface Props {
  /** Every time this number increments (> 0), a new animation plays. */
  trigger: number | null;
  /** Score increase amount (e.g., 10, 15, 25) */
  scoreIncrease: number;
  /** Bonus level for this answer (0 = no bonus, 1 = BQ1, 2 = BQ2, 3+ = BQ3) */
  bonusLevel: number;
  /** Absolute viewport position where animation should originate */
  originPosition: { x: number; y: number } | null;
}

interface FootballCfg {
  initScale: number;
  initX: number;
  angle: number;
  distance: number;
  duration: number;
  delay: number;
  spin: number;
  stretch: number;
}

const MAX_FOOTBALLS = 5;

export function GlobalMultiplayerScoreAnimation({ 
  trigger, 
  scoreIncrease, 
  bonusLevel, 
  originPosition 
}: Props) {
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null);
  
  // Log when component mounts/remounts (since we're using key-based reset)
  useEffect(() => {
    if (trigger) {
      console.log('[GLOBAL_ANIMATION_DEBUG] GlobalMultiplayerScoreAnimation mounted/remounted:', {
        trigger,
        scoreIncrease,
        bonusLevel,
        originPosition,
        portalContainer: !!portalContainer
      });
    }
  }, [trigger, scoreIncrease, bonusLevel, originPosition, portalContainer]);

  useEffect(() => {
    // Find the global animation portal container with retry logic
    const findContainer = () => {
      const container = document.getElementById('global-animation-portal');
      if (container) {
        setPortalContainer(container);
      } else {
        // Retry after a short delay if container not found
        setTimeout(findContainer, 50);
      }
    };
    findContainer();
  }, []);

  // Calculate number of footballs based on bonus level (similar to streak in single player)
  const footballCount = Math.min(Math.max(bonusLevel, 1), MAX_FOOTBALLS);

  const footballConfigs = useMemo((): FootballCfg[] => {
    if (!trigger) return [];

    return Array.from({ length: footballCount }, (_, i): FootballCfg => {
      // Use EXACT same values as singleplayer FootballFx.tsx
      const initScale = 0.9 + Math.random() * 0.2;          // bigger start
      const initX = (Math.random() - 0.5) * 40;

      const angle =
        -Math.PI / 2 + (Math.random() - 0.5) * (Math.PI / 4); // ±22.5°
      const distance = 150 + Math.random() * 80;

      return {
        initScale,
        initX,
        angle,
        distance,
        duration: 0.8 + Math.random() * 0.4,
        delay: i * 0.06,
        spin: (Math.random() - 0.5) * 120,                    // degrees
        stretch: 1.35 + Math.random() * 0.1,                  // juicy overshoot
      };
    });
  }, [footballCount, trigger]);

  // Don't render if no trigger, invalid score, or no position
  // But allow rendering even if portal container isn't found yet (will render to document.body as fallback)
  if (!trigger || scoreIncrease <= 0 || !originPosition) {
    console.log('[GLOBAL_ANIMATION_DEBUG] GlobalMultiplayerScoreAnimation NOT rendering:', {
      trigger: !!trigger,
      scoreIncrease,
      hasOriginPosition: !!originPosition
    });
    return null;
  }

  console.log('[GLOBAL_ANIMATION_DEBUG] GlobalMultiplayerScoreAnimation RENDERING:', {
    trigger,
    scoreIncrease,
    bonusLevel,
    footballCount,
    originPosition,
    portalContainer: !!portalContainer
  });

  // Determine score display text and color based on bonus level
  const getScoreDisplay = () => {
    if (bonusLevel === 0) {
      return { text: `+${scoreIncrease}`, color: 'text-yellow-300' };
    } else if (bonusLevel === 1) {
      return { text: `+${scoreIncrease} BQ1!`, color: 'text-green-300' };
    } else if (bonusLevel === 2) {
      return { text: `+${scoreIncrease} BQ2!`, color: 'text-blue-300' };
    } else {
      return { text: `+${scoreIncrease} BQ3!`, color: 'text-purple-300' };
    }
  };

  const scoreDisplay = getScoreDisplay();

  console.log('[GLOBAL_ANIMATION_DEBUG] Rendering animation content with position:', {
    originPosition,
    footballCount,
    trigger
  });

  const animationContent = (
    <div
      className="fixed pointer-events-none z-[9999] no-debug-box"
      style={{
        left: originPosition.x,
        top: originPosition.y,
        transform: 'translate(-50%, -50%)',
        width: '300px',
        height: '300px'
      }}
    >
      {/* Score Popup - 2.5x larger and positioned at viewport coordinates */}
      <AnimatePresence>
        <motion.div
          key={`score-${trigger}`}
          initial={{ opacity: 1, y: -25, scale: 1.75 }}
          animate={{ opacity: 0, y: -100, scale: 2.75 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 1.2, ease: 'easeOut' }}
          className="absolute left-0 top-0"
        >
          <span
            className={`${scoreDisplay.color} text-2xl font-bold whitespace-nowrap`}
            style={{ textShadow: '2px 2px 4px rgba(0,0,0,0.9)' }}
          >
            {scoreDisplay.text}
          </span>
        </motion.div>
      </AnimatePresence>

      {/* Football Animation - EXACT replication of singleplayer FootballFx.tsx */}
      <AnimatePresence>
        <div
          className="absolute inset-0 flex items-center justify-center pointer-events-none z-40 no-debug-box"
          style={{ transform: "translateY(-150px)", overflow: "visible" }}
        >
          {footballConfigs.map((cfg, i) => {
            // Use EXACT same calculation as singleplayer
            const targetX = cfg.initX + Math.cos(cfg.angle) * cfg.distance * 0.35;
            const targetY = Math.sin(cfg.angle) * cfg.distance;

            return (
              <motion.span
                key={`football-${trigger}-${i}`}
                initial={{
                  opacity: 1,
                  scale: cfg.initScale,
                  x: cfg.initX,
                  y: 0, // EXACT same as singleplayer - start at 0
                  rotate: 0,
                }}
                animate={{
                  opacity: [1, 1, 0],
                  scale: [cfg.initScale, cfg.stretch, cfg.stretch * 0.95],
                  x: [cfg.initX, targetX],
                  y: [0, targetY], // EXACT same as singleplayer - simple 2-step animation
                  rotate: [0, cfg.spin],
                }}
                transition={{
                  duration: cfg.duration,
                  delay: cfg.delay,
                  ease: "easeOut", // EXACT same as singleplayer
                  opacity: {
                    duration: cfg.duration * 0.35,
                    delay: cfg.delay + cfg.duration * 0.65,
                  },
                }}
                exit={{ opacity: 0 }}
                className="text-4xl md:text-5xl select-none" // EXACT same classes as singleplayer
                style={{
                  textShadow: "1px 1px 3px rgba(0,0,0,0.6)", // EXACT same as singleplayer
                  willChange: "transform, opacity",
                  transformOrigin: "center",
                  transform: "translateZ(0)", // GPU promotion
                }}
              >
                🏈
              </motion.span>
            );
          })}
        </div>
      </AnimatePresence>
    </div>
  );

  // Render through portal to the global container, with fallback to document.body
  return createPortal(animationContent, portalContainer || document.body);
}
