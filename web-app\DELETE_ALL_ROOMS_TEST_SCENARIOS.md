# Delete All Rooms - Test Scenarios

## Manual Testing Guide

### Prerequisites
1. Have the application running locally
2. Have at least one user account created
3. Access to the multiplayer lobby

### Test Scenario 1: Basic Functionality
**Objective**: Verify the button appears and works correctly

**Steps**:
1. Sign in to the application
2. Navigate to multiplayer mode
3. Verify the "Delete All Rooms" button appears next to "Refresh List"
4. Create 2-3 waiting rooms using "Host Game"
5. Click "Delete All Rooms"
6. Confirm the deletion in the browser dialog
7. Verify all your waiting rooms are deleted
8. Verify the lobby refreshes automatically

**Expected Results**:
- <PERSON><PERSON> is visible only when signed in
- <PERSON><PERSON> is red with proper styling
- Confirmation dialog appears with warning message
- All user's waiting rooms are deleted
- Success notification appears
- Lobby list refreshes automatically

### Test Scenario 2: No Rooms to Delete
**Objective**: Verify behavior when user has no waiting rooms

**Steps**:
1. Sign in to the application
2. Navigate to multiplayer mode
3. Ensure you have no waiting rooms (delete any existing ones)
4. Click "Delete All Rooms"
5. Confirm the deletion in the browser dialog

**Expected Results**:
- Error message: "No waiting rooms found to delete."
- No database operations performed
- No crashes or unexpected behavior

### Test Scenario 3: Mixed Room Types
**Objective**: Verify only waiting rooms are deleted, not active games

**Steps**:
1. Sign in to the application
2. Create 2 waiting rooms
3. Start one of the rooms (make it active)
4. Return to lobby (leave the active game)
5. Click "Delete All Rooms"
6. Confirm the deletion

**Expected Results**:
- Only the waiting room is deleted
- The active game remains in the database
- Success message mentions only the waiting room

### Test Scenario 4: Permission Verification
**Objective**: Verify users can only delete their own rooms

**Steps**:
1. Sign in as User A
2. Create 2 waiting rooms
3. Sign out and sign in as User B
4. Create 1 waiting room
5. Click "Delete All Rooms"
6. Confirm the deletion

**Expected Results**:
- Only User B's room is deleted
- User A's rooms remain untouched
- Success message shows only 1 room deleted

### Test Scenario 5: Cancellation
**Objective**: Verify canceling the confirmation dialog works

**Steps**:
1. Sign in to the application
2. Create 2-3 waiting rooms
3. Click "Delete All Rooms"
4. Click "Cancel" in the confirmation dialog

**Expected Results**:
- No rooms are deleted
- No database operations performed
- User returns to normal lobby view

### Test Scenario 6: Error Handling
**Objective**: Verify graceful error handling

**Steps**:
1. Sign in to the application
2. Create 1-2 waiting rooms
3. Disconnect from internet
4. Click "Delete All Rooms"
5. Confirm the deletion

**Expected Results**:
- Error message appears in the lobby error area
- No crashes or unexpected behavior
- Button becomes enabled again after error

### Test Scenario 7: Loading State
**Objective**: Verify loading states work correctly

**Steps**:
1. Sign in to the application
2. Create several waiting rooms
3. Click "Delete All Rooms"
4. Confirm the deletion
5. Observe the button text during the operation

**Expected Results**:
- Button text changes to "Deleting..." during operation
- Button is disabled during operation
- Loading state is cleared after completion

### Test Scenario 8: Unauthenticated User
**Objective**: Verify button is not visible to unauthenticated users

**Steps**:
1. Ensure you are signed out
2. Navigate to multiplayer mode
3. Look for the "Delete All Rooms" button

**Expected Results**:
- Button is not visible
- Only "Refresh List" button appears
- No errors or crashes

## Automated Testing Considerations

### Unit Tests
- Test `handleDeleteAllRooms` function with mocked Supabase calls
- Test confirmation dialog behavior
- Test error handling paths
- Test loading state management

### Integration Tests
- Test database operations with real Supabase instance
- Test RLS policy enforcement
- Test foreign key constraint handling

### E2E Tests
- Test complete user flow from creation to deletion
- Test multi-user scenarios
- Test network failure scenarios

## Performance Considerations

### Large Number of Rooms
If testing with many rooms (>10), consider:
- Database query performance
- UI responsiveness during deletion
- Memory usage during bulk operations

### Concurrent Operations
Test scenarios where:
- Multiple users delete rooms simultaneously
- Rooms are being created while deletion is in progress
- Network requests overlap

## Security Testing

### RLS Policy Verification
- Verify users cannot delete other users' rooms
- Verify only waiting rooms can be deleted
- Test with different user roles/permissions

### SQL Injection Prevention
- Verify parameterized queries are used
- Test with malicious input (though unlikely in this context)

## Browser Compatibility

Test the confirmation dialog and button behavior across:
- Chrome
- Firefox
- Safari
- Edge

## Mobile Testing

Verify the button layout and functionality on:
- Mobile browsers
- Different screen sizes
- Touch interactions
