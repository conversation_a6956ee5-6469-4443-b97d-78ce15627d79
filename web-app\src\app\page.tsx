'use client'; // This page now needs client-side interactivity

// Force dynamic rendering to avoid SSR issues temporarily
export const dynamic = 'force-dynamic';

import { useEffect, useState, Suspense, useCallback, useRef, useMemo } from 'react';
import nextDynamic from 'next/dynamic';
import { motion } from 'framer-motion';
import { useGameStore } from '@/stores/gameStore';
import { PlayerImageDisplay } from '@/components/game/PlayerImageDisplay';
import { ChoiceButton } from '@/components/game/ChoiceButton';
import { RecentAnswersList } from '@/components/game/RecentAnswersList';
import { PlayerInfoPanel } from '@/components/game/PlayerInfoPanel';
import { Button } from '@/components/ui/button'; // For Start/Next button
import type { PlayerData, GameModeType } from '@/types';
import { ScorePopup } from '@/components/game/ScorePopup';
import { FootballFx } from '@/components/game/FootballFx';
import { FootballLoader } from '@/components/game/FootballLoader';
import { cn } from '@/lib/utils'; // For conditional class names
import { TimeChangePopup } from '@/components/game/TimeChangePopup';
import { flushSync } from 'react-dom';

import { GlobalMultiplayerScoreAnimation } from '@/components/game/GlobalMultiplayerScoreAnimation';
import { GlobalFallbackAnimation } from '@/components/game/GlobalFallbackAnimation';
import { useElementPosition } from '@/hooks/useElementPosition';
import { useOnlineStatus } from '@/hooks/useOnlineStatus';
import { DisconnectedOverlay } from '@/components/ui/DisconnectedOverlay';
import AuthModal from '@/components/auth/AuthModal';
import { User } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabaseClient';

import { RealtimeChannel } from '@supabase/supabase-js';


type OverallGameType = 'single-player' | 'multiplayer';
type MultiplayerPanelState = 'lobby_list' | 'in_room';
type CenterPanelMpState = 'lobby_list_detail' | 'expanded_leaderboard' | 'mp_game_active';
type MultiplayerGameMode = 'competitive' | 'cooperative';

// Define Leaderboard types
interface LeaderboardEntry {
  rank: number;
  username: string;
  score: number;
  userId?: string;
}

interface RegionalLeaderboard {
  regionName: string;
  entries: LeaderboardEntry[];
}

// Update the game player types to use a single consistent type
type GamePlayer = {
  user_id: string;
  is_connected: boolean;
  is_ready?: boolean;
  profile?: { username: string | null } | null;
};

// Add type for answer
type GameAnswer = {
  userId: string;
  questionId: string;
  choiceName: string;
  timestamp: number;
  isCorrect: boolean;
};

// Update GameRoom interface to use GameAnswer type
interface GameRoom {
  id: string;
  created_at: string;
  status: 'waiting' | 'active' | 'finished';
  host_id: string;
  multiplayer_mode: MultiplayerGameMode | null;
  title: string | null;
  room_code: string | null;
  max_players: number;
  profiles: { username: string | null } | null;
  game_players: GamePlayer[];
  player_count: number;
  connected_players: number;
  original_player_ids?: string[] | null; // Array of user_ids who were in the room when game started
  current_question_data?: PlayerQuestion;
  player_scores?: Record<string, number>;
  player_bonus_levels?: Record<string, number>;
  current_round_answers?: GameAnswer[];
  current_round_number?: number;
  game_start_timestamp?: string | null;
  current_round_ends_at?: string | null;
}

// Add PlayerChoice type
interface PlayerChoice {
  name: string;
  isCorrect: boolean;
}

// Add PlayerQuestion type
interface PlayerQuestion {
  questionId: string; // Unique ID for this question instance
  correctPlayerId: number;
  imageUrl: string;
  choices: PlayerChoice[];
  correctChoiceName: string; // Added for answer validation
  correctPlayer?: PlayerData; // Optional, for client-side display if needed
}

// Add type definition at the top level
type UserProfile = {
  id: string;
  username: string | null;
};

// Add new type for players in room - consolidated and enhanced
type PlayerInRoom = {
  user_id: string;
  profile: { username: string | null } | null;
  is_ready?: boolean;
  is_connected: boolean;
  gamePlayerId?: string; // The ID from the game_players table (optional for backward compatibility)
  roomId?: string; // Room ID (optional for backward compatibility)
  joined_at?: string; // Join timestamp (optional for backward compatibility)
};

// Add new types for game settings
type GameDuration = 30 | 60 | 90 | 120;
type MaxPlayers = 2 | 4 | 6 | 8;

// Add state for tracking animated answers
interface AnimatedAnswersState {
  [questionId: string]: Set<string>; // questionId -> Set of userIds that have been animated
}

// Add interface for enhanced animation data
interface PlayerAnimationData {
  trigger: number;
  scoreIncrease: number;
  bonusLevel: number;
  questionId: string;
}

interface EnhancedAnimatedAnswersState {
  [userId: string]: PlayerAnimationData;
}

// Define the possible connection states for the realtime subscriptions
type ConnectionStatus = 'INITIALIZING' | 'CONNECTED' | 'RECONNECTING' | 'OFFLINE';

// Connection Status Indicator Component
const ConnectionStatusIndicator = ({ status }: { status: ConnectionStatus }) => {
  switch (status) {
    // In the ideal state, render nothing at all.
    case 'CONNECTED':
    case 'INITIALIZING':
      return null;

    // When reconnecting, show a calm, temporary message.
    case 'RECONNECTING':
      return (
        <div className="reconnecting-indicator flex items-center justify-center gap-2 bg-yellow-900/50 text-yellow-200 p-2 rounded text-xs mb-3 text-center w-full border border-yellow-600/30">
          <div className="spinner w-4 h-4 border-2 border-yellow-400 border-t-transparent rounded-full animate-spin"></div>
          <span>Reconnecting...</span>
        </div>
      );

    // If you implement a fully offline state, show a persistent message.
    case 'OFFLINE':
      return (
        <div className="offline-indicator bg-red-900/50 text-red-200 p-2 rounded text-xs mb-3 text-center w-full border border-red-600/30">
          <span>You are offline. Please check your connection.</span>
        </div>
      );

    default:
      return null;
  }
};



function HomePageContent() {
  // Use searchParams only on client side to avoid SSR issues
  const [clientSearchParams, setClientSearchParams] = useState<URLSearchParams | null>(null);
  
  useEffect(() => {
    // Only access search params on client side
    if (typeof window !== 'undefined') {
      setClientSearchParams(new URLSearchParams(window.location.search));
    }
  }, []);

  // Overall game type state (moved to top)
  const [selectedOverallGameType, setSelectedOverallGameType] = useState<OverallGameType>('single-player');

  // Add debug logging for state changes
  console.log("HomePageContent render - Current selectedOverallGameType:", selectedOverallGameType);

  // Add hasMounted state for hydration handling
  const [hasMounted, setHasMounted] = useState(false);

  // Layer 1: The Proactive Client - Track browser online status
  const isOnline = useOnlineStatus();


  // Get state and actions from the store
  const {
    loadPlayers,
    setGameMode,
    nextQuestion,
    submitAnswer,
    resetCurrentModeGame,
    currentQuestion,
    score,
    streak,
    bestStreak,
    bestNormalScore,
    bestTimedScore,
    isAnswered,
    recentAnswers,
    gameStatus,
    activeGameMode,
    lastScoreChange,
    animationTrigger,
    isLoadingInitialGame,
    loadingMessage,
    timer,
    isCountdownActive,
    countdownValue,
    userChoiceName,
    lastTimeChange,
    timeChangeAnimationTrigger,
  } = useGameStore();

  // Existing state
  const [selectedPlayerInfo, setSelectedPlayerInfo] = useState<PlayerData | null>(null);
  const [viewingRecentPlayer, setViewingRecentPlayer] = useState<PlayerData | null>(null);

  // New multiplayer state
  const [multiplayerPanelState, setMultiplayerPanelState] = useState<MultiplayerPanelState>('lobby_list');
  const [centerPanelMpState, setCenterPanelMpState] = useState<CenterPanelMpState>('lobby_list_detail');
  const [activeRoomId, setActiveRoomId] = useState<string | null>(null);
  const [selectedRoomForDetail, setSelectedRoomForDetail] = useState<GameRoom | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loadingUser, setLoadingUser] = useState(true);
  const [loadingProfile, setLoadingProfile] = useState(false);
  // Add authentication loading state to prevent race condition
  const [isAuthLoading, setIsAuthLoading] = useState(true);
  const [gameRooms, setGameRooms] = useState<GameRoom[]>([]);
  const [isLoadingRooms, setIsLoadingRooms] = useState(false);
  const [errorMp, setErrorMp] = useState<string | null>(null);
  const [lobbyFetchError, setLobbyFetchError] = useState<boolean>(false);
  const [newRoomMode, setNewRoomMode] = useState<MultiplayerGameMode>('competitive');

  // Leaderboard States
  const [personalRecords, setPersonalRecords] = useState<LeaderboardEntry[]>([]);
  const [globalLeaderboard, setGlobalLeaderboard] = useState<LeaderboardEntry[]>([]);
  const [regionalLeaderboards, setRegionalLeaderboards] = useState<RegionalLeaderboard[]>([]);
  const [userRegion, setUserRegion] = useState<string | null>(null);
  const [expandedLeaderboardData, setExpandedLeaderboardData] = useState<{title: string, entries: LeaderboardEntry[]} | null>(null);

  // Update userProfile state with the new type
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);

  // Add new state for players in room
  const [playersInRoom, setPlayersInRoom] = useState<PlayerInRoom[]>([]);
  const [isLoadingPlayers, setIsLoadingPlayers] = useState(false);

  // Add new state for current room's game data
  const [currentRoomGameData, setCurrentRoomGameData] = useState<GameRoom | null>(null);

  // Add new state for game settings
  const [selectedGameDuration, setSelectedGameDuration] = useState<GameDuration>(60);
  const [selectedMaxPlayers, setSelectedMaxPlayers] = useState<MaxPlayers>(4);
  const [isCreatingRoom, setIsCreatingRoom] = useState(false);
  const [isStartingGame, setIsStartingGame] = useState(false); // New state for preventing double-start



  // Add state for tracking animated answers
  const [animatedAnswers, setAnimatedAnswers] = useState<AnimatedAnswersState>({});

  // Add state for enhanced animation data
  const [enhancedAnimatedAnswers, setEnhancedAnimatedAnswers] = useState<EnhancedAnimatedAnswersState>({});

  // Add state for answer submission
  const [isSubmittingAnswer, setIsSubmittingAnswer] = useState(false);
  const [hasSubmittedCurrentRound, setHasSubmittedCurrentRound] = useState(false);
  const [submittedAnswerForQuestion, setSubmittedAnswerForQuestion] = useState<string | null>(null);

  // Add state for next question functionality
  const [isAdvancingToNextQuestion, setIsAdvancingToNextQuestion] = useState(false);

  // Add optimistic answer state for snappy UI updates
  const [optimisticAnswer, setOptimisticAnswer] = useState<{
    userId: string;
    questionId: string;
    choiceName: string;
    timestamp: number;
    isCorrect: boolean;
    isPending: boolean;
  } | null>(null);

  // Add state for preventing multiple ready state submissions
  const [isSubmittingReady, setIsSubmittingReady] = useState(false);

  // Add state for debug show answer feature
  const [showDebugAnswer, setShowDebugAnswer] = useState(false);
  
  // Add state for preventing multiple leave room submissions
  const [isLeavingRoom, setIsLeavingRoom] = useState(false);

  // Add state for join/rejoin loading feedback
  const [isJoiningOrRejoiningRoom, setIsJoiningOrRejoiningRoom] = useState(false);

  // Add state for preventing URL parameter interference during room creation
  const [isCreatingAndJoiningRoom, setIsCreatingAndJoiningRoom] = useState(false);

  // Add connection status state for realtime subscriptions
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('INITIALIZING');

  // 1. Add setHasAnswered state
  const [hasAnswered, setHasAnswered] = useState(false);

  // 2. Add triggeredAnimationsRef
  const triggeredAnimationsRef = useRef<Set<string>>(new Set());

  // Add useRef for tracking subscription channels to prevent "nuke and pave" issues
  const roomSubscriptionsRef = useRef<RealtimeChannel[]>([]);

  // Add useRef to prevent React Strict Mode double-running auth effect
  const authEffectRan = useRef(false);

  // 3. Add setGlobalAnimations and globalAnimations
  const [globalAnimations, setGlobalAnimations] = useState<Array<{
    id: string;
    type: 'enhanced' | 'fallback';
    trigger: number;
    scoreIncrease: number;
    bonusLevel: number;
    originPosition: { x: number; y: number };
  }>>([]);

  // 4. Change getElementPosition to useElementPosition
  const getElementPosition = useElementPosition();

  // 5. Add animatingAnswers and setAnimatingAnswers
  const [animatingAnswers, setAnimatingAnswers] = useState<Set<string>>(new Set());

  // 6. Add allAnswers using useMemo
  const allAnswers = useMemo(() => {
    const realTimeAnswers: (GameAnswer & { isOptimistic?: boolean })[] = Array.isArray(currentRoomGameData?.current_round_answers)
      ? currentRoomGameData.current_round_answers.map(answer => ({ ...answer, isOptimistic: false }))
      : [];

    // Include optimistic answer if it exists and matches current question
    const currentQuestionId = currentRoomGameData?.current_question_data?.questionId;

    // Use a map to deduplicate answers by user/question combination
    const answerMap = new Map<string, GameAnswer & { isOptimistic?: boolean }>();

    for (const ans of realTimeAnswers) {
      const key = `${ans.userId}-${ans.questionId}`;
      answerMap.set(key, ans);
    }

    if (optimisticAnswer && optimisticAnswer.questionId === currentQuestionId) {
      const key = `${optimisticAnswer.userId}-${optimisticAnswer.questionId}`;
      answerMap.set(key, { ...optimisticAnswer, isOptimistic: true });
    }

    const finalAnswers = Array.from(answerMap.values());
    
    console.log('[ALL_ANSWERS_DEBUG] Constructed allAnswers:', {
      realTimeAnswersCount: realTimeAnswers.length,
      hasOptimisticAnswer: !!optimisticAnswer,
      optimisticMatchesCurrentQuestion: optimisticAnswer?.questionId === currentQuestionId,
      finalAnswersCount: finalAnswers.length,
      currentUserId: user?.id,
      finalAnswers: finalAnswers.map(ans => ({
        userId: ans.userId.substring(0, 8) + '...',
        isCurrentUser: ans.userId === user?.id,
        choiceName: ans.choiceName,
        isCorrect: ans.isCorrect,
        isOptimistic: ans.isOptimistic
      }))
    });

    return finalAnswers;
  }, [currentRoomGameData?.current_round_answers, currentRoomGameData?.current_question_data?.questionId, optimisticAnswer, user?.id]);

  // 7. Add handleNextQuestion if not present
  const handleNextQuestion = useCallback(async () => {
    if (!user?.id || !activeRoomId || isAdvancingToNextQuestion) {
      return;
    }

    // DIAGNOSTIC: Log what we're about to send to the server
    console.log('[DIAGNOSTIC] handleNextQuestion called - BEFORE server call');
    console.log('[DIAGNOSTIC] Client-side data at time of next question request:', {
      activeRoomId,
      userId: user.id,
      currentRoomGameData: {
        status: currentRoomGameData?.status,
        host_id: currentRoomGameData?.host_id,
        current_question_data: currentRoomGameData?.current_question_data ? {
          questionId: currentRoomGameData.current_question_data.questionId,
          correctPlayerId: currentRoomGameData.current_question_data.correctPlayerId,
          choicesCount: currentRoomGameData.current_question_data.choices.length
        } : null,
        current_round_answers: currentRoomGameData?.current_round_answers,
        current_round_answers_type: Array.isArray(currentRoomGameData?.current_round_answers) ? 'array' : typeof currentRoomGameData?.current_round_answers,
        current_round_answers_length: Array.isArray(currentRoomGameData?.current_round_answers) ? currentRoomGameData.current_round_answers.length : 'N/A'
      },
      playersInRoom: {
        count: playersInRoom.length,
        userIds: playersInRoom.map(p => p.user_id)
      },
      // Calculate the same logic as the UI
      clientSideCalculation: (() => {
        const realTimeAnswers = Array.isArray(currentRoomGameData?.current_round_answers)
          ? currentRoomGameData.current_round_answers
          : [];
        const currentQuestionId = currentRoomGameData?.current_question_data?.questionId;
        const answersForCurrentQuestion = realTimeAnswers.filter(answer =>
          answer.questionId === currentQuestionId
        );
        return {
          realTimeAnswersType: Array.isArray(realTimeAnswers) ? 'array' : typeof realTimeAnswers,
          realTimeAnswersLength: realTimeAnswers.length,
          currentQuestionId,
          answersForCurrentQuestion: answersForCurrentQuestion,
          answersForCurrentQuestionCount: answersForCurrentQuestion.length,
          playersInRoomCount: playersInRoom.length,
          allPlayersSubmitted: answersForCurrentQuestion.length === playersInRoom.length
        };
      })()
    });

    setIsAdvancingToNextQuestion(true);

    try {
      const { data, error } = await supabase.functions.invoke('next-question-handler', {
        body: { roomId: activeRoomId },
      });

      console.log('[DIAGNOSTIC] Server response received:', { data, error });

      if (error) {
        console.log('[DIAGNOSTIC] Server returned error:', error);
        setErrorMp(`Failed to advance to next question: ${error.message}`);
      } else {
        console.log('[DIAGNOSTIC] Server call successful:', data);
      }
    } catch (e) {
      console.log('[DIAGNOSTIC] Exception during server call:', e);
      setErrorMp('Failed to advance to next question. Please try again.');
    } finally {
      setIsAdvancingToNextQuestion(false);
    }
  }, [user?.id, activeRoomId, isAdvancingToNextQuestion, currentRoomGameData, playersInRoom]);

  // **CRITICAL: TOP-LEVEL RENDER STATE CHECK - This should show the actual state being used in this render cycle**
  console.log('[RENDER HOST] *** TOP-LEVEL RENDER STATE CHECK *** HomePageContent render cycle. playersInRoom:', JSON.parse(JSON.stringify(playersInRoom)));

  // **CRITICAL: COMPREHENSIVE RENDER LOGGING - Track every re-render and state changes**
  const currentRoomDetails = gameRooms.find(room => room.id === activeRoomId);
  const isCurrentUserHost = currentRoomDetails?.host_id === user?.id;
  const renderLogPrefix = isCurrentUserHost ? '[RENDER HOST]' : '[RENDER]';
  
  console.log(`${renderLogPrefix} *** HomePageContent RE-RENDER *** Component render cycle:`, {
    timestamp: new Date().toISOString(),
    renderNumber: Math.floor(Date.now() / 1000), // Approximate render sequence
    keyStateValues: {
      selectedOverallGameType,
      multiplayerPanelState,
      centerPanelMpState,
      activeRoomId,
      hasUser: !!user,
      userId: user?.id,
      isCurrentUserHost,
      playersInRoomCount: playersInRoom.length,
      playersInRoomUserIds: playersInRoom.map(p => p.user_id),
      playersInRoomUsernames: playersInRoom.map(p => p.profile?.username || 'NO_USERNAME'),
      playersReady: playersInRoom.filter(p => p.is_ready).length,
      currentRoomStatus: currentRoomGameData?.status,
      isLoadingPlayers,
      hasCurrentRoomGameData: !!currentRoomGameData
    },
    componentWillRender: {
      shouldShowPlayerList: selectedOverallGameType === 'multiplayer' && multiplayerPanelState === 'in_room' && currentRoomGameData?.status === 'waiting',
      shouldShowStartButton: selectedOverallGameType === 'multiplayer' && isCurrentUserHost && currentRoomGameData?.status === 'waiting',
      shouldShowNeedMorePlayersMessage: selectedOverallGameType === 'multiplayer' && isCurrentUserHost && playersInRoom.length < 2,
      playersInRoomIsEmpty: playersInRoom.length === 0,
      allPlayersReady: playersInRoom.every(p => p.is_ready)
    }
  });

  // **CRITICAL: Add specific logging for the "Need X more players" logic**
  if (selectedOverallGameType === 'multiplayer' && isCurrentUserHost && currentRoomGameData?.status === 'waiting') {
    const playerCountForButton = playersInRoom.length;
    const needMorePlayers = playerCountForButton < 2;
    const playersNeeded = needMorePlayers ? 2 - playerCountForButton : 0;
    
    console.log(`${renderLogPrefix} [START_BUTTON_LOGIC] Host rendering Start Game button logic:`, {
      playerCountForButton,
      needMorePlayers,
      playersNeeded,
      buttonTextWillBe: needMorePlayers ? `Need ${playersNeeded} more player(s)` : (playersInRoom.every(p => p.is_ready) ? "Start Game" : "Waiting for all to ready..."),
      buttonWillBeDisabled: needMorePlayers || !playersInRoom.every(p => p.is_ready) || isStartingGame,
      playersInRoomDetailed: playersInRoom.map(p => ({
        userId: p.user_id,
        username: p.profile?.username,
        isReady: p.is_ready,
        isConnected: p.is_connected
      })),
      timestamp: new Date().toISOString()
    });
  }

  // **CRITICAL: Add specific logging for the player list rendering**
  if (selectedOverallGameType === 'multiplayer' && multiplayerPanelState === 'in_room' && currentRoomGameData?.status === 'waiting') {
    console.log(`${renderLogPrefix} [PLAYER_LIST_LOGIC] Rendering player list in waiting room:`, {
      playersInRoomCount: playersInRoom.length,
      maxPlayers: currentRoomGameData.max_players,
      displayString: `Players (${playersInRoom.length}/${currentRoomGameData.max_players || 8})`,
      isLoadingPlayers,
      willShowLoadingMessage: isLoadingPlayers,
      willShowWaitingMessage: !isLoadingPlayers && playersInRoom.length === 0,
      willShowPlayerList: !isLoadingPlayers && playersInRoom.length > 0,
      playersToRender: playersInRoom.map(p => ({
        userId: p.user_id,
        username: p.profile?.username || `Player...${p.user_id.slice(-4)}`,
        isReady: p.is_ready,
        isCurrentUser: p.user_id === user?.id,
        isHost: currentRoomGameData?.host_id === p.user_id
      })),
      timestamp: new Date().toISOString()
    });
  }

  // Load player data on initial mount
  useEffect(() => {
    // Initial load only if players aren't loaded yet
    if (!useGameStore.getState().players.length) {
      loadPlayers();
    }
  }, [loadPlayers]);

  // Add effect for client-side mounting
  useEffect(() => {
    setHasMounted(true);
  }, []);

  // Add enhanced sign-out handler before the existing useEffect
  const handleSignOut = async () => {
    console.log('[SignOut] Enhanced sign-out process initiated', {
      hasActiveRoom: !!activeRoomId,
      activeRoomId,
      userId: user?.id,
      timestamp: new Date().toISOString()
    });

    // Note: Room cleanup will happen automatically via the auth listener's comprehensive state reset
    // The auth listener will clear all multiplayer states including activeRoomId when user signs out
    console.log('[SignOut] Room cleanup will be handled by auth listener state reset');

    // Perform Supabase sign-out
    console.log('[SignOut] Proceeding with Supabase sign-out');
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error('[SignOut] Error during Supabase sign-out:', error);
      setErrorMp(`Sign-out error: ${error.message}`);
    } else {
      console.log('[SignOut] Supabase sign-out successful - auth listener will handle state reset');
    }
  };

  // Effect for handling authentication state changes
  useEffect(() => {
    // Only run the effect logic if we're not in strict mode's second render
    if (authEffectRan.current === false) {
      console.log('[AuthEffect] Running initial auth setup.');
      console.log('[AuthEffect] ENHANCED DEBUG: Current auth state when effect runs:', {
        userId: user?.id,
        isAuthLoading,
        loadingUser,
        authEffectHasRun: authEffectRan.current,
        timestamp: new Date().toISOString()
      });

      const { data: authListener } = supabase.auth.onAuthStateChange((event, session) => {
        console.log(`[AuthListener] Event: ${event}, Session User: ${session?.user?.id}`);
        console.log(`[AuthListener] DEBUGGING: Setting user state. Current user: ${user?.id}, New user: ${session?.user?.id}`);
        
        // CRITICAL FIX: Enhanced logging to track auth state changes
        console.log(`[AuthListener] ENHANCED DEBUG: Auth state change details:`, {
          event,
          hasSession: !!session,
          sessionUserId: session?.user?.id,
          currentUserId: user?.id,
          willUpdateUser: session?.user?.id !== user?.id,
          timestamp: new Date().toISOString()
        });
        
        setUser(session?.user ?? null);
        if (!session?.user) { // If user is signed out
          setUserProfile(null); // Clear profile
          if (selectedOverallGameType === 'multiplayer' && event === 'SIGNED_OUT') {
            handleOverallGameTypeChange('single-player');
          }
        }
        
        // ENHANCED: More comprehensive state reset on logout
        if (event === 'SIGNED_OUT') {
          console.log('[AuthListener] SIGNED_OUT - Performing COMPREHENSIVE client state reset.');

          // Primary multiplayer states
          setActiveRoomId(null);
          setCurrentRoomGameData(null);
          setPlayersInRoom([]);
          setSelectedRoomForDetail(null);
          setMultiplayerPanelState('lobby_list');
          setCenterPanelMpState('lobby_list_detail');
          setGameRooms([]); // Clear the list of game rooms

          // CRITICAL FIX: Reset loading state to prevent permanent loading screen
          setIsLoadingRooms(false); // Ensure the loading flag is reset!

          // UI and loading states
          // NOTE: Do NOT set isLoadingRooms to true here - let the next fetch handle it
          setErrorMp(null); // Clear any multiplayer errors
          setIsLeavingRoom(false); // Reset leaving state
          setIsSubmittingReady(false); // Reset ready submission state
          setIsCreatingRoom(false); // Reset room creation state
          setIsStartingGame(false); // Reset game starting state
          setIsJoiningOrRejoiningRoom(false); // Reset joining state
          setIsCreatingAndJoiningRoom(false); // Reset creating and joining state
          
          // Expanded view states
          setExpandedLeaderboardData(null);
          setPersonalRecords([]);
          setGlobalLeaderboard([]);
          setRegionalLeaderboards([]);
          setUserRegion(null);
          
          // Animation and optimistic update states
          setAnimatedAnswers({});
          setEnhancedAnimatedAnswers({});
          
          console.log('[AuthListener] COMPREHENSIVE client state reset complete for SIGNED_OUT.');
        }
        
        // Auto-run debug check on sign in for debugging purposes
        if (event === 'SIGNED_IN' && session?.user) {
          console.log('[AuthListener] SIGNED_IN detected - verifying clean state before proceeding');
          
          // Verify critical states are clean before proceeding
          console.log('[AuthListener] Post-signin state verification:', {
            activeRoomId: activeRoomId, // Should be null
            selectedRoomForDetail: selectedRoomForDetail, // Should be null
            playersInRoom: playersInRoom.length, // Should be 0
            multiplayerPanelState, // Should be 'lobby_list'
            centerPanelMpState, // Should be 'lobby_list_detail'
            gameRooms: gameRooms.length, // Should be 0 initially
            timestamp: new Date().toISOString()
          });
        }
        
        // Profile fetching will be handled by the effect below, triggered by 'user' changing
        if (['INITIAL_SESSION', 'SIGNED_IN', 'SIGNED_OUT'].includes(event)) {
            setLoadingUser(false);
        }
      });

      // Initial session check (only sets user, profile fetch is separate)
      supabase.auth.getSession().then(({ data: { session }, error }) => {
        console.log("[AuthEffect] Initial getSession complete. Session:", session);
        console.log("[AuthEffect] Session user object:", session?.user);
        
        if (error) {
          console.error('[AuthEffect] Error getting session:', error);
          setUser(null);
        } else {
          setUser(session?.user ?? null);
        }
        
        setLoadingUser(false); // Loading user is done after initial session check
        console.log("[AuthEffect] Set user state to:", session?.user?.id);
      }).catch((e) => {
        console.error('[AuthEffect] Exception in getSession:', e);
        setUser(null);
        setLoadingUser(false);
      }).finally(() => {
        // THIS IS THE MOST IMPORTANT PART!
        // Signal that the initial auth check is done.
        console.log('[AuthEffect] Auth loading finished.');
        setIsAuthLoading(false);
      });

      // Mark that the effect has run
      authEffectRan.current = true;

      // The cleanup will still be registered but only for the final unmount
      return () => {
        console.log('[AuthEffect] Cleaning up the auth listener on component unmount.');
        authListener?.subscription.unsubscribe();
      };
    } else {
      console.log('[AuthEffect] SKIPPING auth setup - already ran (Strict Mode protection).');
    }
  }, [
    selectedOverallGameType,
    activeRoomId,
    centerPanelMpState,
    gameRooms.length,
    multiplayerPanelState,
    playersInRoom.length,
    selectedRoomForDetail
    // CRITICAL FIX: Removed user?.id from dependency array to prevent circular dependency
    // that causes auth listener to be re-subscribed during login, disrupting auth state sync
  ]); // Re-run if selectedOverallGameType or related states change

  // Effect for fetching user profile when user object changes and is not null
  useEffect(() => {
    const fetchProfile = async () => {
      if (user?.id) {
        setLoadingProfile(true); // Indicate profile loading starts
        console.log(`[ProfileFetcher] Attempting to fetch profile for user ID: ${user.id}`);
        try {
          const { data, error, status } = await supabase
            .from('profiles')
            .select('id, username')
            .eq('id', user.id)
            .single();

          if (error && status !== 406) {
            console.error("[ProfileFetcher] Error fetching user profile:", error.message);
            setUserProfile(null);
          } else if (data) {
            console.log("[ProfileFetcher] User profile fetched:", data);
            setUserProfile(data);
          } else {
            console.warn(`[ProfileFetcher] No profile found for user ID: ${user.id}`);
            setUserProfile(null);
          }
        } catch (e) {
          console.error("[ProfileFetcher] Exception fetching user profile:", e);
          setUserProfile(null);
        } finally {
          setLoadingProfile(false); // Indicate profile loading ends
        }
      } else {
        // If no user, ensure profile is null and not loading
        setUserProfile(null);
        setLoadingProfile(false);
      }
    };

    fetchProfile();
  }, [user]); // This effect runs whenever the 'user' object changes

  // Add automatic disconnect detection and connection management
  useEffect(() => {
    if (!user?.id || !activeRoomId) {
      return; // No cleanup needed if not logged in or not in a room
    }

    console.log('[DISCONNECT_DETECTION] Setting up disconnect detection for user:', {
      userId: user.id,
      activeRoomId,
      timestamp: new Date().toISOString()
    });

    // Function to mark player as disconnected
    const markPlayerDisconnected = async (reason: string) => {
      console.log(`[DISCONNECT_DETECTION] Marking player as disconnected. Reason: ${reason}`, {
        userId: user.id,
        activeRoomId,
        timestamp: new Date().toISOString()
      });

      try {
        const { error } = await supabase
          .from('game_players')
          .update({
            is_connected: false,
            last_seen_at: new Date().toISOString()
          })
          .eq('room_id', activeRoomId)
          .eq('user_id', user.id);

        if (error) {
          console.error('[DISCONNECT_DETECTION] Error marking player as disconnected:', error);
        } else {
          console.log('[DISCONNECT_DETECTION] Successfully marked player as disconnected');
        }
      } catch (e) {
        console.error('[DISCONNECT_DETECTION] Exception marking player as disconnected:', e);
      }
    };

    // Handle browser tab close/refresh
    const handleBeforeUnload = () => {
      console.log('[DISCONNECT_DETECTION] beforeunload event triggered');

      // Mark player as disconnected (this is a best-effort attempt)
      // Note: This may not always complete due to browser limitations
      markPlayerDisconnected('beforeunload');

      // Don't show confirmation dialog for multiplayer games
      // Users should be able to leave/refresh freely and reconnect
    };

    // Handle page visibility changes (tab switching, minimizing)
    const handleVisibilityChange = async () => {
      try {
        if (document.hidden) {
          console.log('[DISCONNECT_DETECTION] Page became hidden (tab switch/minimize)');
          // We can let supabase handle the disconnect naturally.
        } else {
          console.log('[DISCONNECT_DETECTION] Page became visible - forcing data resync.');

          // NO MORE setConnectionStatus('RECONNECTING')! This is the key change.
          // The user doesn't need to see this.

          supabase.realtime.connect(); // Gently ask Supabase to reconnect.

          if (user?.id) {
            try {
              const { error: profileUpdateError } = await supabase
                .from('profiles')
                .update({ last_seen_at: new Date().toISOString() })
                .eq('id', user.id);
              if (profileUpdateError) {
                // Use warn to avoid Next.js error overlay for this non-critical failure
                console.warn(
                  '[DISCONNECT_DETECTION] Failed to update profile last_seen_at:',
                  profileUpdateError?.message || JSON.stringify(profileUpdateError)
                );
              } else {
                console.log('[DISCONNECT_DETECTION] Updated profile last_seen_at');
              }
            } catch (err) {
              console.warn(
                '[DISCONNECT_DETECTION] Exception updating profile last_seen_at:',
                err instanceof Error ? err.message : err
              );
            }
          }

          // If in a room, silently refresh all state to ensure we haven't missed anything.
          if (activeRoomId) {
            console.log('[DISCONNECT_DETECTION] Silently refreshing full room state.');
            const { data: roomData } = await supabase.from('game_rooms').select('*').eq('id', activeRoomId).single();
            if (roomData) {
              setCurrentRoomGameData(roomData);
              await fetchPlayersInActiveRoom(activeRoomId, 'visibility_change');
            }
          }

          // No need to set to CONNECTED, as we removed the RECONNECTING state.
          console.log('[DISCONNECT_DETECTION] Silent data resync complete.');
        }
      } catch (err) {
        // Catch any unexpected errors to avoid unhandled promise rejections
        console.warn(
          '[DISCONNECT_DETECTION] handleVisibilityChange encountered an error:',
          err instanceof Error ? err.message : err
        );
      }
    };

    // Add event listeners with promise rejection handling
    window.addEventListener('beforeunload', handleBeforeUnload);
    const visibilityWrapper = () => {
      handleVisibilityChange().catch(err =>
        console.warn(
          '[DISCONNECT_DETECTION] handleVisibilityChange rejected:',
          err instanceof Error ? err.message : err
        )
      );
    };
    document.addEventListener('visibilitychange', visibilityWrapper);

    // Cleanup function
    return () => {
      console.log('[DISCONNECT_DETECTION] Cleaning up disconnect detection listeners');
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', visibilityWrapper);
    };
  }, [user?.id, activeRoomId]); // Re-run when user or room changes

  // Add fetchAndSetGameRooms function after the state declarations
  const fetchAndSetGameRooms = useCallback(async () => {
    // Guard against auth loading state
    if (isAuthLoading) {
      console.log('[LOBBY_FETCH] Skipping fetchAndSetGameRooms - auth still loading');
      return;
    }

    // **NEW: Prevent redundant fetches with loading state guard**
    if (isLoadingRooms) {
      console.log('[LOBBY_FETCH] Fetch already in progress, skipping to prevent redundant calls');
      return;
    }

    console.log('[LOBBY_FETCH] *** CRITICAL DEBUG START *** fetchAndSetGameRooms CALLED for user:', {
      userId: user?.id,
      timestamp: new Date().toISOString(),
      userIsAuthenticated: !!user,
      purpose: 'Investigating lobby player count display issue'
    });

    console.log('[LOBBY_FETCH] *** QUERY CONSTRUCTION ANALYSIS *** About to construct Supabase query');
    setIsLoadingRooms(true);
    setErrorMp(null);
    setLobbyFetchError(false);
    
    // **CRITICAL FIX**: Fetch individual player records instead of count aggregation
    // This should work better with RLS policies
    console.log('[LOBBY_FETCH] *** CRITICAL QUERY DETAILS *** Constructing query with the following specifications:');
    console.log('[LOBBY_FETCH] - Table: game_rooms');
    console.log('[LOBBY_FETCH] - Status filter: [waiting, active] (to show joinable rooms)');
    console.log('[LOBBY_FETCH] - Player data method: Fetching individual game_players records with user_id and is_connected');
    console.log('[LOBBY_FETCH] - User context: Fetching for user', user?.id);
    console.log('[LOBBY_FETCH] - Expected behavior: Should return ALL waiting/active rooms with player data for reconnection logic');
    
    // **ENHANCED SELECT STRING**: Fetch individual player records for reconnection logic
    const selectQueryString = `
      id,
      title,
      status,
      room_code,
      multiplayer_mode,
      host_id,
      created_at,
      max_players,
      original_player_ids,
      profiles:host_id (
        username
      ),
      game_players (
        user_id,
        is_connected
      )
    `;
    
    console.log('[LOBBY_FETCH] *** SELECT STRING VERIFICATION *** Using exact select string:', {
      selectQueryString: selectQueryString.trim(),
      includesGamePlayersRecords: selectQueryString.includes('game_players ('),
      includesUserIdField: selectQueryString.includes('user_id'),
      includesIsConnectedField: selectQueryString.includes('is_connected'),
      userContext: user?.id
    });

    // Base query: select rooms that are 'waiting' or 'active' for rejoining
    const query = supabase
      .from('game_rooms')
      .select(selectQueryString)
      .in('status', ['waiting', 'active']) // Show waiting AND active games for rejoining
      .order('created_at', { ascending: false });

    // **CRITICAL**: Log the exact query structure before execution
    console.log('[LOBBY_FETCH] *** QUERY OBJECT INSPECTION *** Final query object constructed:', {
      queryDetails: {
        table: 'game_rooms',
        selectFields: selectQueryString.split('\n').map(s => s.trim()).filter(s => s),
        statusFilter: ['waiting', 'active'],
        orderBy: 'created_at desc',
        playerDataMethod: 'game_players individual records',
        userContext: user?.id,
        expectedToSeeRoomsHostedByOthers: true,
        potentialIssue: 'If this returns empty game_players arrays, check game_players RLS policies for individual record access'
      }
    });

    console.log('[LOBBY_FETCH] *** ABOUT TO EXECUTE QUERY *** Calling Supabase with constructed query');
    
    try {
      const { data: fetchedRooms, error } = await query;

      console.log('[LOBBY_FETCH] *** RAW QUERY RESULT *** Supabase query completed:', {
        success: !error,
        error: error ? JSON.stringify(error, null, 2) : null,
        resultCount: fetchedRooms?.length || 0,
        userWhoExecutedQuery: user?.id,
        timestamp: new Date().toISOString()
      });

      // **CRITICAL DEBUG**: Log the raw data structure from Supabase
      if (fetchedRooms && fetchedRooms.length > 0) {
        console.log('[LOBBY_FETCH] *** RAW DATA STRUCTURE INSPECTION *** First room raw data:', {
          firstRoom: JSON.stringify(fetchedRooms[0], null, 2),
          gamePlayersStructure: fetchedRooms[0]?.game_players,
          gamePlayersType: typeof fetchedRooms[0]?.game_players,
          gamePlayersIsArray: Array.isArray(fetchedRooms[0]?.game_players),
          hasPlayerRecords: Array.isArray(fetchedRooms[0]?.game_players) && fetchedRooms[0]?.game_players.length > 0,
          userContext: user?.id
        });
      }

      if (error) {
        console.error('[LOBBY_FETCH] *** QUERY ERROR *** Error fetching game_rooms:', {
          errorMessage: error.message,
          errorCode: error.code,
          errorDetails: error.details,
          errorHint: error.hint,
          userContext: user?.id,
          possibleCauses: [
            'RLS policy blocking access to game_rooms',
            'RLS policy blocking game_players count aggregation',
            'Network connectivity issue',
            'Database permissions problem',
            'Query syntax error'
          ]
        });
        setGameRooms([]); // Set to empty on error
        setLobbyFetchError(true);
        setErrorMp(`Failed to fetch room details: ${error.message}`);
      } else {
        console.log('[LOBBY_FETCH] *** SUCCESSFUL RAW DATA *** Query successful, processing results:', {
          totalRoomsFromDB: fetchedRooms?.length || 0,
          userExecutingQuery: user?.id,
          roomsBreakdown: fetchedRooms?.map(room => ({
            roomId: room.id.substring(0, 8) + '...',
            title: room.title,
            status: room.status,
            hostId: room.host_id.substring(0, 8) + '...',
            rawGamePlayersData: room.game_players,
            isHostedByCurrentUser: room.host_id === user?.id,
            isHostedByOtherUser: room.host_id !== user?.id
          })) || [],
          criticalCheck: {
            shouldSeeFreshsRoom: 'Check if any room has hostId matching fresh user',
            fresh2CanSeeRoomsHostedByOthers: fetchedRooms?.some(room => room.host_id !== user?.id) || false
          }
        });
        
        const enrichedRooms = fetchedRooms?.map(room => {
          // **CRITICAL PLAYER COUNT EXTRACTION**: Handle the count aggregation result
          let playerCount = 0;
          
          console.log('[LOBBY_FETCH] *** PLAYER COUNT EXTRACTION *** Processing room for player count:', {
            roomId: room.id,
            roomTitle: room.title,
            hostId: room.host_id,
            rawGamePlayersData: room.game_players,
            gamePlayersType: typeof room.game_players,
            gamePlayersIsArray: Array.isArray(room.game_players),
            gamePlayersLength: room.game_players?.length,
            firstElementIfArray: Array.isArray(room.game_players) ? room.game_players[0] : null
          });
          
          // Process individual player records instead of count aggregation
          if (Array.isArray(room.game_players)) {
            playerCount = room.game_players.length;
            const connectedPlayerCount = room.game_players.filter(p => p.is_connected).length;
            console.log('[LOBBY_FETCH] *** PLAYER RECORDS SUCCESS *** Extracted player data:', {
              roomId: room.id,
              totalPlayers: playerCount,
              connectedPlayers: connectedPlayerCount,
              playerRecords: room.game_players.map(p => ({ user_id: p.user_id, is_connected: p.is_connected }))
            });
          } else {
            console.warn('[LOBBY_FETCH] *** PLAYER RECORDS ERROR *** game_players is not an array:', {
              roomId: room.id,
              gamePlayersType: typeof room.game_players,
              gamePlayersValue: room.game_players,
              fallbackCount: playerCount
            });
          }
          
          console.log('[LOBBY_FETCH] *** ROOM ENRICHMENT *** Processing room for display:', {
            roomId: room.id,
            roomTitle: room.title,
            hostId: room.host_id,
            status: room.status,
            finalPlayerCount: playerCount,
            maxPlayers: room.max_players,
            isHostedByCurrentUser: room.host_id === user?.id,
            profilesData: room.profiles
          });
          
          // Handle profiles data safely - Supabase joins can return array or single object
          let hostUsername: string | null = null;
          if (room.profiles) {
            if (Array.isArray(room.profiles)) {
              hostUsername = room.profiles[0]?.username || null;
            } else {
              hostUsername = (room.profiles as { username: string | null })?.username || null;
            }
          }
          
          return {
            id: room.id,
            created_at: room.created_at,
            status: room.status,
            host_id: room.host_id,
            multiplayer_mode: room.multiplayer_mode,
            title: room.title,
            room_code: room.room_code,
            max_players: room.max_players,
            original_player_ids: room.original_player_ids, // **CRITICAL**: Include original player IDs for reconnection logic
            profiles: hostUsername ? { username: hostUsername } : null,
            game_players: room.game_players || [], // **CRITICAL**: Keep individual player data for reconnection logic
            player_count: playerCount,
            connected_players: Array.isArray(room.game_players) ? room.game_players.filter(p => p.is_connected).length : 0
          };
        }) || [];
        
        console.log('[LOBBY_FETCH] *** FINAL ENRICHED RESULTS *** Setting gameRooms state:', {
          enrichedRoomsCount: enrichedRooms.length,
          userContext: user?.id,
          finalRoomsForUI: enrichedRooms.map(room => ({
            id: room.id.substring(0, 8) + '...',
            title: room.title,
            hostId: room.host_id.substring(0, 8) + '...',
            status: room.status,
            playerCount: room.player_count,
            connectedPlayers: room.connected_players,
            isOwnedByCurrentUser: room.host_id === user?.id,
            displayText: `Players: ${room.player_count}/${room.max_players || 8}`
          })),
          criticalAnalysis: {
            totalRoomsToShow: enrichedRooms.length,
            roomsHostedByCurrentUser: enrichedRooms.filter(r => r.host_id === user?.id).length,
            roomsHostedByOthers: enrichedRooms.filter(r => r.host_id !== user?.id).length,
            waitingRooms: enrichedRooms.filter(r => r.status === 'waiting').length,
            roomsWithZeroPlayers: enrichedRooms.filter(r => r.player_count === 0).length,
            roomsWithPositivePlayerCount: enrichedRooms.filter(r => r.player_count > 0).length,
            shouldBeVisibleToFresh2: 'Check if fresh2 can see correct player counts for rooms hosted by fresh'
          }
        });
        
        setGameRooms(enrichedRooms as unknown as GameRoom[]); // Ensure GameRoom type matches
        
        if (enrichedRooms.length === 0 && fetchedRooms && fetchedRooms.length > 0) {
          console.warn('[LOBBY_FETCH] *** DATA TRANSFORMATION WARNING *** Enriched rooms is empty, but raw DB data was not!', { 
            rawDataCount: fetchedRooms.length,
            enrichedRoomsCount: enrichedRooms.length,
            rawDataSample: fetchedRooms[0],
            possibleIssue: 'Data transformation problem'
          });
        }
        setLobbyFetchError(false); // Clear error state on success
      }
    } catch (e) {
      const error = e instanceof Error ? e : new Error(String(e));
      console.error('[LOBBY_FETCH] *** EXCEPTION *** Exception fetching game_rooms:', {
        exceptionMessage: error.message,
        exceptionName: error.name,
        exceptionStack: error.stack,
        userContext: user?.id,
        timestamp: new Date().toISOString()
      });
      setErrorMp(`Exception fetching rooms: ${error.message}`);
      setLobbyFetchError(true);
      setGameRooms([]);
    } finally {
      setIsLoadingRooms(false);
      console.log('[LOBBY_FETCH] *** CRITICAL DEBUG END *** fetchAndSetGameRooms FINISHED for user:', {
        userId: user?.id,
        timestamp: new Date().toISOString(),
        finalState: 'isLoadingRooms set to false'
      });
    }
  }, [user, user?.id, isLoadingRooms, isAuthLoading]); // Include user and loading states in dependency array



  // Update handleOverallGameTypeChange to use fetchAndSetGameRooms
  const handleOverallGameTypeChange = useCallback((type: OverallGameType) => {
    console.log("handleOverallGameTypeChange called with:", type, "Current type:", selectedOverallGameType);
    setSelectedOverallGameType(type);
    
    if (type === 'multiplayer') {
      console.log("Switching to multiplayer mode");
      // ENHANCED: Ensure clean state when switching to multiplayer
      setMultiplayerPanelState('lobby_list');
      setCenterPanelMpState('lobby_list_detail');
      setActiveRoomId(null);
      setSelectedRoomForDetail(null);
      setCurrentRoomGameData(null);
      setPlayersInRoom([]);
      setErrorMp(null);
      
      // Guard against auth loading before fetching rooms
      if (!isAuthLoading) {
        // Fetch rooms when switching to multiplayer
        fetchAndSetGameRooms();
      }
    } else {
      console.log("Switching to single-player mode");
      resetCurrentModeGame();
    }
  }, [fetchAndSetGameRooms, resetCurrentModeGame, isAuthLoading]);

  // Handler for clicking a recent answer
  const handleRecentSelect = (player: PlayerData) => {
    setSelectedPlayerInfo(player);
    setViewingRecentPlayer(player);
  };

  // Clear selected player info on next question or new game
  const handleNextQuestionClick = () => {
    setSelectedPlayerInfo(null);
    setViewingRecentPlayer(null);
    nextQuestion();
  };

  // Handler for returning to game from recent player view
  const handleReturnToGame = () => {
    setViewingRecentPlayer(null);
    const { isAnswered: currentIsAnswered, currentQuestion: gameCurrentQuestion } = useGameStore.getState();
    if (currentIsAnswered && gameCurrentQuestion && gameCurrentQuestion.correctPlayer) {
        setSelectedPlayerInfo(gameCurrentQuestion.correctPlayer);
    } else {
        setSelectedPlayerInfo(null);
    }
  };

  // Handler for resetting the game
  const handleGameResetClick = () => {
    setSelectedPlayerInfo(null);
    setViewingRecentPlayer(null);
    resetCurrentModeGame();
  };

  // Handler for switching game mode
  const handleModeButtonClick = (mode: GameModeType) => {
    setViewingRecentPlayer(null);
    setSelectedPlayerInfo(null);
    setGameMode(mode);
  };

  const handleJoinRoom = useCallback(async (roomId: string, autoJoinedAfterCreate = false) => {
    console.log(`[Client] *** HANDLEJOINROOM ENTRY *** handleJoinRoomAttempt called for room: ${roomId}. Current activeRoomId: ${activeRoomId}, User: ${user?.id}`);
    console.log(`[Client] *** CRITICAL DEBUG *** Room ID analysis:`, {
      roomIdParameter: roomId,
      roomIdType: typeof roomId,
      roomIdLength: roomId?.length,
      autoJoinedAfterCreate,
      currentActiveRoomId: activeRoomId,
      currentUserId: user?.id,
      timestamp: new Date().toISOString(),
      isCreatingAndJoiningRoom,
      isJoiningOrRejoiningRoom
    });

    if (!user?.id) {
      console.warn('[Client] Join attempt: user state missing. Verifying session directly.');
      try {
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
        if (sessionError) {
          console.error('[Client] getSession error during join attempt:', sessionError);
        }
        if (sessionData?.session?.user) {
          console.log('[Client] Session user found. Updating user state and continuing join.');
          setUser(sessionData.session.user);
        } else {
          console.error('[Client] Join attempt: No user session found after verification.');
          setErrorMp("You must be logged in to join or rejoin a room.");
          return;
        }
      } catch (sessionException) {
        console.error('[Client] Exception verifying session during join attempt:', sessionException);
        setErrorMp("You must be logged in to join or rejoin a room.");
        return;
      }
    }

    // Prevent multiple simultaneous join attempts
    if (isJoiningOrRejoiningRoom) {
      console.warn('[Client] Join attempt already in progress, ignoring duplicate request.');
      return;
    }

    // Let React useEffect hooks manage subscription lifecycle cleanly
    console.log('[Client] Joining room - React useEffect hooks will handle subscription transitions');

    setIsJoiningOrRejoiningRoom(true);
    setErrorMp(null);
    
    if (activeRoomId && activeRoomId !== roomId) {
      // This implies user is trying to join a NEW room while already in another.
      console.warn(`[Client] Join attempt: User is already in activeRoomId ${activeRoomId}, attempting to join ${roomId}. This might need explicit leave first.`);
      // For now, we'll allow it and let the server-side logic handle it
    }

    try {
        console.log(`[Client] *** STARTING JOIN PROCESS *** User ${user?.id} attempting to join/rejoin room: ${roomId} via server-side logic.`);
        
        // **CRITICAL ENHANCEMENT**: Streamlined validation for host auto-join scenarios
        if (autoJoinedAfterCreate && isCreatingAndJoiningRoom) {
            console.log(`[Client] *** HOST AUTO-JOIN DETECTED *** This is a host auto-joining their newly created room. Skipping client-side player existence checks.`);
                            console.log(`[Client] *** HOST AUTO-JOIN CONTEXT ***`, {
                roomId,
                hostUserId: user?.id,
                autoJoinedAfterCreate,
                isCreatingAndJoiningRoom,
                rationale: "Host should not exist in game_players yet for this brand new room",
                timestamp: new Date().toISOString()
            });
        } else {
            console.log(`[Client] *** STANDARD JOIN/REJOIN *** Performing full validation checks.`);
            
            // Check if already connected client-side to this exact room
            if (activeRoomId === roomId && playersInRoom.some(p => p.user_id === user?.id)) {
                console.log(`[Client] User ${user?.id} is ALREADY CONNECTED client-side to room ${roomId}. Proceeding to room view if not already there.`);
                setMultiplayerPanelState('in_room');
                setCenterPanelMpState('mp_game_active');
                setIsJoiningOrRejoiningRoom(false);
                return;
            }
        }

        // Fetch room details for validation (always needed)
        console.log(`[Client] *** STEP 1: ROOM VALIDATION *** Fetching room details for validation`);

        const { data: roomValidationData, error: roomValidationError } = await supabase
            .from('game_rooms')
            .select('id, status, host_id, max_players, title')
            .eq('id', roomId)
            .maybeSingle();

        if (roomValidationError) {
            console.error(`[Client] PRE-INSERT VALIDATION: FAILED - Error fetching room details for ${roomId}:`, roomValidationError);
            setErrorMp(`Room validation failed: ${roomValidationError.message}`);
            setIsJoiningOrRejoiningRoom(false);
            return;
        }

        if (!roomValidationData) {
            console.error(`[Client] PRE-INSERT VALIDATION: FAILED - Room ${roomId} not found`);
            console.log(`[Client] STALE ROOM DETECTED: Room not found, likely cleaned up by janitor. Refreshing lobby.`);
            setErrorMp("Oops! That game is no longer available. The lobby has been refreshed.");

            // Auto-refresh the lobby to remove stale rooms
            setTimeout(async () => {
                console.log(`[Client] Auto-refreshing lobby after room not found`);
                await fetchAndSetGameRooms();
                // Clear the error after refresh
                setTimeout(() => setErrorMp(null), 3000);
            }, 1000);

            setIsJoiningOrRejoiningRoom(false);
            return;
        }

        console.log(`[Client] STEP 1 COMPLETE: Room validation successful:`, roomValidationData);

        // **CRITICAL ENHANCEMENT**: Different logic paths for host auto-join vs. standard join
        if (autoJoinedAfterCreate && isCreatingAndJoiningRoom && roomValidationData.host_id === user?.id) {
            console.log(`[Client] *** HOST AUTO-JOIN PATH *** Confirmed host auto-joining their own newly created room`);
            console.log(`[Client] *** STEP 2 (HOST AUTO-JOIN): SKIP PLAYER EXISTENCE CHECK *** For brand new room, host should not exist in game_players yet`);
            
            // For host auto-join, we skip the player existence check and go straight to INSERT
            // because this is a brand new room and the host should not exist in game_players yet
            console.log(`[Client] *** STEP 3 (HOST AUTO-JOIN): DIRECT INSERT *** Proceeding with host INSERT to game_players`);
            
        } else {
            console.log(`[Client] *** STANDARD JOIN PATH *** Performing full player existence validation`);
            
            // **STEP 2**: Check if player already exists in the room (for standard joins)
            console.log(`[Client] *** STEP 2: PLAYER EXISTENCE CHECK *** Checking if player ${user?.id} is already in game_players for room ${roomId}`);
            
            const { data: existingPlayerData, error: existingPlayerError } = await supabase
                .from('game_players')
                .select('user_id, is_connected, is_ready')
                .eq('room_id', roomId)
                .eq('user_id', user?.id)
                .maybeSingle();

            if (existingPlayerError) {
                console.error(`[Client] PRE-INSERT VALIDATION: FAILED - Error checking existing player for room ${roomId}:`, existingPlayerError);
                setErrorMp(`Player validation failed: ${existingPlayerError.message}`);
                setIsJoiningOrRejoiningRoom(false);
                return;
            }

            if (existingPlayerData) {
                // Player EXISTS - this is a REJOIN scenario
                console.log(`[Client] STEP 2 COMPLETE: Player ${user?.id} EXISTS in game_players for room ${roomId}. This is a REJOIN.`, existingPlayerData);
                console.log(`[Client] *** REJOIN VALIDATION *** Checking if rejoin is allowed based on room status: ${roomValidationData.status}`);
                
                // For rejoins, we allow both 'waiting' and 'active' rooms (since user was already a player)
                if (roomValidationData.status === 'finished') {
                    console.error(`[Client] REJOIN VALIDATION: FAILED - Cannot rejoin finished game`);
                    setErrorMp(`Cannot rejoin: Game has finished.`);
                    setIsJoiningOrRejoiningRoom(false);
                    return;
                }
                
                if (existingPlayerData.is_connected) {
                    console.log(`[Client] Player ${user?.id} is already marked as connected to room ${roomId}. Setting client state to match.`);
                } else {
                    console.log(`[Client] Player ${user?.id} exists but is disconnected. Updating to connected for reconnection.`);

                    // Enhanced reconnection with last_seen_at update
                    const { error: updateError } = await supabase
                        .from('game_players')
                        .update({
                            is_connected: true,
                            last_seen_at: new Date().toISOString()
                        })
                        .eq('room_id', roomId)
                        .eq('user_id', user?.id);

                    if (updateError) {
                        console.error(`[Client] Failed to update connection status for rejoin:`, updateError);
                        setErrorMp(`Failed to rejoin room: ${updateError.message}`);
                        setIsJoiningOrRejoiningRoom(false);
                        return;
                    }

                    console.log(`[Client] Successfully updated connection status and last_seen_at for reconnection`);
                }
                
                // Proceed to set client state for rejoin
                console.log(`[Client] REJOIN: Setting client state for room ${roomId} with status ${roomValidationData.status}`);
                setActiveRoomId(roomId);
                setMultiplayerPanelState('in_room');
                setCenterPanelMpState('mp_game_active');
                setSelectedRoomForDetail(null);
                
                if (autoJoinedAfterCreate) {
                    await fetchAndSetGameRooms(); // Refresh lobby list to show the new room
                }
                
                setIsJoiningOrRejoiningRoom(false);
                return;
            } else {
                // Player does NOT exist - this is a NEW JOIN scenario
                console.log(`[Client] STEP 2 COMPLETE: Player ${user?.id} does NOT exist in game_players for room ${roomId}. This is a NEW JOIN.`);
                
                // **STEP 3**: Additional validation for new joins
                console.log(`[Client] *** STEP 3: NEW JOIN VALIDATION *** Performing additional checks for new join`);
                
                // Check if room status is still joinable
                console.log(`[Client] *** STEP 3.1: STATUS VALIDATION *** Checking room status for new join. Current status: ${roomValidationData.status}`);
                if (roomValidationData.status !== 'waiting') {
                    console.error(`[Client] PRE-INSERT VALIDATION: FAILED - Room ${roomId} is no longer in 'waiting' status. Current status: ${roomValidationData.status}`);

                    // Enhanced error handling for different room states
                    let errorMessage: string;
                    if (roomValidationData.status === 'active') {
                        // Check if this user was an original player (for potential rejoin)
                        const { data: originalPlayerData, error: originalPlayerError } = await supabase
                            .from('game_rooms')
                            .select('original_player_ids')
                            .eq('id', roomId)
                            .maybeSingle();

                        if (!originalPlayerError && originalPlayerData?.original_player_ids?.includes(user?.id)) {
                            errorMessage = `Game is already active, but you are an original player. Try rejoining instead of joining as new.`;
                            console.log(`[Client] STATUS VALIDATION: User ${user?.id} is an original player of active room ${roomId}. Suggesting rejoin.`);
                        } else {
                            errorMessage = `Cannot join: Game is already active and you are not an original player.`;
                            console.log(`[Client] STATUS VALIDATION: User ${user?.id} is NOT an original player of active room ${roomId}.`);
                        }
                    } else if (roomValidationData.status === 'finished') {
                        errorMessage = `Cannot join: Game has finished.`;
                    } else {
                        errorMessage = `Cannot join: Room status is '${roomValidationData.status}'. Only waiting rooms can be joined.`;
                    }

                    // Show user-friendly error and auto-refresh lobby
                    console.log(`[Client] STALE ROOM DETECTED: Showing user-friendly error and refreshing lobby`);
                    setErrorMp(`Oops! That game is no longer available. The lobby has been refreshed.`);

                    // Auto-refresh the lobby to remove stale rooms
                    setTimeout(async () => {
                        console.log(`[Client] Auto-refreshing lobby after stale room detection`);
                        await fetchAndSetGameRooms();
                        // Clear the error after refresh
                        setTimeout(() => setErrorMp(null), 3000);
                    }, 1000);

                    setIsJoiningOrRejoiningRoom(false);
                    return;
                }

                // Check room capacity
                const { count: validationConnectedCount, error: validationCountError } = await supabase
                    .from('game_players')
                    .select('*', { count: 'exact', head: true })
                    .eq('room_id', roomId)
                    .eq('is_connected', true);

                if (validationCountError) {
                    console.error(`[Client] PRE-INSERT VALIDATION: FAILED - Error counting connected players for room ${roomId}:`, validationCountError);
                    setErrorMp(`Room validation failed: ${validationCountError.message}`);
                    setIsJoiningOrRejoiningRoom(false);
                    return;
                }

                const currentCount = validationConnectedCount || 0;
                const maxPlayers = roomValidationData.max_players || 8;
                if (currentCount >= maxPlayers) {
                    console.error(`[Client] PRE-INSERT VALIDATION: FAILED - Room ${roomId} is at capacity. Players: ${currentCount}/${maxPlayers}`);
                    setErrorMp(`Cannot join: Room is full (${currentCount}/${maxPlayers} players).`);
                    setIsJoiningOrRejoiningRoom(false);
                    return;
                }
                
                console.log(`[Client] STEP 3 COMPLETE: New join validation successful. Room has capacity: ${currentCount}/${maxPlayers}`);
            }
        }

        // **STEP 4**: Perform the INSERT for new joins (both host auto-join and standard new joins reach here)
        console.log(`[Client] *** STEP 4: INSERT ATTEMPT *** Attempting INSERT into game_players`);
        
        const insertData = {
            room_id: roomId,
            user_id: user?.id,
            is_connected: true,
            is_ready: false,
            last_seen_at: new Date().toISOString()
        };
        
        console.log(`[Client] VALIDATED INSERT: Attempting INSERT into game_players with validated data:`, insertData);
        
        const { data: insertedPlayerData, error: insertError } = await supabase
            .from('game_players')
            .insert(insertData)
            .select('user_id, room_id, is_connected, is_ready')
            .single();

        if (insertError) {
            console.error(`[Client] *** CRITICAL ERROR *** INSERT into game_players FAILED:`, {
                error: insertError,
                message: insertError.message,
                code: insertError.code,
                details: insertError.details,
                hint: insertError.hint,
                insertData,
                roomId,
                userId: user?.id,
                autoJoinedAfterCreate,
                isCreatingAndJoiningRoom,
                timestamp: new Date().toISOString()
            });

            // Special handling for duplicate key errors (409 conflicts)
            if (insertError.code === '23505' || insertError.message?.includes('duplicate key')) {
                console.error(`[Client] *** DUPLICATE KEY VIOLATION *** This suggests a race condition or the user already exists in the room:`, {
                    possibleCauses: [
                        "Race condition: Multiple join attempts for the same user",
                        "User already exists but client-side check missed it",
                        "Previous join attempt left orphaned record"
                    ],
                    roomId,
                    userId: user?.id,
                    autoJoinedAfterCreate,
                    currentTimestamp: new Date().toISOString()
                });
                setErrorMp("Join failed: You may already be in this room or there was a timing conflict. Please refresh and try again.");
            } else {
                setErrorMp(`Failed to join room: ${insertError.message}`);
            }

            // THE FIX: If join fails, we need to re-establish global subscriptions
            // since we nuked them at the beginning of handleJoinRoom
            console.log('[Client] [JOIN_FAILED] Re-establishing global subscriptions after failed join attempt');
            // The global subscriptions useEffect will automatically run when activeRoomId is null
            // and user is authenticated, so we don't need to do anything explicit here

            setIsJoiningOrRejoiningRoom(false);
            return;
        }

        console.log(`[Client] Step 4 complete: User ${user?.id} successfully made NEW JOIN (inserted) to game_players for room ${roomId}. Data:`, insertedPlayerData);

        // Standard join/rejoin successful - set client active state
        console.log(`[Client] Standard Join/Rejoin successful for room ${roomId}. Setting client active state.`);
        setActiveRoomId(roomId);
        setMultiplayerPanelState('in_room');
        setCenterPanelMpState('mp_game_active');
        setSelectedRoomForDetail(null);

        // Reset connection status to initializing when joining a room
        setConnectionStatus('INITIALIZING');
        
        console.log(`[Client] Client active state set for room ${roomId}. Awaiting data fetches and UI transition.`);
        
        if (autoJoinedAfterCreate) {
            await fetchAndSetGameRooms(); // Refresh lobby list to show the new room
        }
        
        // Fetch updated room state
        const { data: updatedRoomState } = await supabase
            .from('game_rooms')
            .select('*')
            .eq('id', roomId)
            .maybeSingle();
        if (updatedRoomState) setCurrentRoomGameData(updatedRoomState as GameRoom);
        
        // CRITICAL FIX: Proactively fetch player list immediately after successful join
        // This eliminates the "jankiness" where players don't see each other until realtime updates arrive
        console.log('[Client] [JOIN_SUCCESS] Proactively fetching player list to eliminate delay');
        await fetchPlayersInActiveRoom(roomId, 'join_success_proactive');

        await fetchAndSetGameRooms(); // Refresh lobby in case player counts changed
        setIsJoiningOrRejoiningRoom(false);

    } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        console.error(`[Client] *** EXCEPTION *** Error in handleJoinRoomAttempt for room ${roomId}:`, {
            error: err,
            message: err.message,
            stack: err.stack,
            roomId,
            userId: user?.id,
            autoJoinedAfterCreate,
            isCreatingAndJoiningRoom,
            timestamp: new Date().toISOString()
        });
        setErrorMp(`Failed to join/rejoin room (exception): ${err.message}`);
        setIsJoiningOrRejoiningRoom(false);
    }
  }, [
    user?.id,
    activeRoomId,
    playersInRoom,
    isJoiningOrRejoiningRoom,
    isCreatingAndJoiningRoom,
    fetchAndSetGameRooms
  ]);

  const handleViewLobbyDetail = (room: GameRoom) => {
    console.log("[LobbyDetail] Viewing room:", room.id, "Current gameRooms:", gameRooms);
    setSelectedRoomForDetail(room);
    setCenterPanelMpState('lobby_list_detail');
    setExpandedLeaderboardData(null);
    setActiveRoomId(null);
    setMultiplayerPanelState('lobby_list');
  };

  const handleExpandLeaderboard = (title: string, entries: LeaderboardEntry[]) => {
    setExpandedLeaderboardData({ title, entries });
    setCenterPanelMpState('expanded_leaderboard');
    setSelectedRoomForDetail(null);
    setActiveRoomId(null);
  };

  // Update handleLeaveRoom to use the Edge Function
  const handleLeaveRoom = async () => {
    if (!activeRoomId || !user) {
      console.warn("[Client] Cannot leave room: no room or user.");
      setErrorMp("Cannot leave room: missing room or user information.");
      return;
    }

    // 💡 THE CRITICAL GUARD CLAUSE - Prevent React Strict Mode double-invocation
    if (isLeavingRoom) {
      console.warn('[Client] Leave action already in progress. Ignoring duplicate call.');
      return;
    }

    console.log(`[Client] [LEAVE_ROOM] User ${user.id} attempting to leave room ${activeRoomId}`);
    setErrorMp(null); // Clear any previous errors

    try {
      // 1. Set the flag to true IMMEDIATELY
      setIsLeavingRoom(true);
      // Call the leave-room-handler Edge Function
      console.log(`[Client] [LEAVE_ROOM] Invoking leave-room-handler for room ${activeRoomId}`);
      const { data, error: invokeError } = await supabase.functions.invoke('leave-room-handler', {
        body: { roomId: activeRoomId },
      });

      if (invokeError) {
        console.error("[Client] Error invoking leave-room-handler:", invokeError);
        let errorMessage = 'Failed to leave room.';
        
        if (invokeError.context && invokeError.context.json) {
          const errorData = invokeError.context.json;
          errorMessage = errorData.error || errorData.message || invokeError.message;
        } else {
          errorMessage = invokeError.message || 'Unknown server error';
        }
        
        console.error("[Client] [LEAVE_ROOM_ERROR] Setting error message:", errorMessage);
        setErrorMp(errorMessage);
        return;
      }

      console.log("[Client] [LEAVE_ROOM_SUCCESS] Successfully left room:", data);
      
      // Update UI state immediately
      setActiveRoomId(null);
      setMultiplayerPanelState('lobby_list');
      setCenterPanelMpState('lobby_list_detail');
      setPlayersInRoom([]);
      setCurrentRoomGameData(null);
      
      // Clear any room-specific errors
      if (errorMp) {
        setErrorMp(null);
      }
      
      // Refresh the game rooms list to reflect changes
      console.log("[Client] [LEAVE_ROOM] Refreshing game rooms list...");
      await fetchAndSetGameRooms();
      
      // Show success message
      const roomDeleted = data.roomDeleted;
      const remainingPlayers = data.remainingPlayersCount;
      
      if (roomDeleted) {
        console.log(`[Client] [LEAVE_ROOM] Room was deleted (was empty)`);
        // Optional: Show toast or notification
      } else {
        console.log(`[Client] [LEAVE_ROOM] Room kept with ${remainingPlayers} remaining players`);
      }

    } catch (e) {
      const error = e instanceof Error ? e : new Error(String(e));
      console.error("[Client] Client-side exception leaving room:", error);
      setErrorMp(`Client-side exception: ${error.message}`);
    } finally {
      // 4. IMPORTANT: Unset the flag in a finally block
      // This ensures it gets reset even if an error occurs.
      setIsLeavingRoom(false);
    }
  };

  // Determine which player info to show in the right panel
  const playerToShow = viewingRecentPlayer 
                        ? viewingRecentPlayer 
                        : (selectedPlayerInfo ? selectedPlayerInfo : (isAnswered && currentQuestion ? currentQuestion.correctPlayer : null));

  // Format timer for display
  const formattedTimer = `${String(Math.floor(timer / 60)).padStart(1, '0')}:${String(timer % 60).padStart(2, '0')}`;

  // Update the roomCode handling useEffect to use fetchAndSetGameRooms
  useEffect(() => {
    const roomCodeFromUrl = clientSearchParams?.get('roomCode');
    console.log("[DEBUG] Detected roomCodeFromUrl:", roomCodeFromUrl, "isCreatingAndJoiningRoom:", isCreatingAndJoiningRoom);

    // **CRITICAL FIX**: Don't process URL room codes during room creation to prevent race conditions
    if (roomCodeFromUrl && !loadingUser && !isCreatingAndJoiningRoom) {
      if (!user) {
        setErrorMp("Please log in to join the room.");
        return;
      }

      if (selectedOverallGameType !== 'multiplayer') {
        handleOverallGameTypeChange('multiplayer');
      }
      
      const joinRoomByCode = async (code: string) => {
        setIsLoadingRooms(true);
        setErrorMp(null);
        try {
          const { data: roomData, error: roomError } = await supabase
            .from('game_rooms')
            .select('id, status, title, multiplayer_mode, profiles:host_id (username), created_at, host_id')
            .eq('room_code', code)
            .maybeSingle();

          if (roomError) throw roomError;

          if (roomData) {
            if (roomData.status === 'waiting') {
              await handleJoinRoom(roomData.id);
              // Refresh lobby after joining
              await fetchAndSetGameRooms();
            } else {
              setErrorMp(`Room '${roomData.title || code}' is no longer waiting or has already started.`);
              setSelectedRoomForDetail(roomData as unknown as GameRoom);
              setCenterPanelMpState('lobby_list_detail');
            }
          } else {
            setErrorMp(`Room with code "${code}" not found.`);
          }
        } catch (e) {
          const error = e instanceof Error ? e : new Error(String(e));
          console.error("[DEBUG] Error joining by room code:", error);
          setErrorMp(`Error finding or joining room: ${error.message}`);
        } finally {
          setIsLoadingRooms(false);
        }
      };

      if (multiplayerPanelState === 'lobby_list' || selectedOverallGameType === 'multiplayer') {
        joinRoomByCode(roomCodeFromUrl);
      }
    }
  }, [clientSearchParams, user, loadingUser, isCreatingAndJoiningRoom, handleOverallGameTypeChange, multiplayerPanelState, selectedOverallGameType, handleJoinRoom, fetchAndSetGameRooms]);

  // Update handleCreateRoom to include game settings
  const handleCreateRoom = async () => {
    console.log('[Client] [CREATE_ROOM] handleCreateRoom called');

    if (!user || !user.id) {
      console.error('[Client] Cannot create room: user not authenticated');
      setErrorMp('Cannot create room: user not authenticated.');
      return;
    }

    if (isCreatingRoom) {
      console.warn('[Client] Room creation already in progress, ignoring duplicate request');
      return;
    }

    console.log(`[Client] [CREATE_ROOM] User ${user.id} attempting to create room`);
    setIsCreatingRoom(true);
    setErrorMp(null); // Clear any previous errors
    
    try {
      // Step 1: Check for and clean up any existing rooms hosted by this user
      console.log(`[Client] [CREATE_ROOM] Checking for existing rooms hosted by user ${user.id}...`);
      const { data: existingHostRooms, error: fetchHostRoomsError } = await supabase
        .from('game_rooms')
        .select(`
          id,
          status,
          title,
          created_at,
          game_players (
            user_id,
            is_connected
          )
        `)
        .eq('host_id', user.id)
        .in('status', ['waiting', 'active']); // Only non-completed rooms

      if (fetchHostRoomsError) {
        console.error('[Client] [CREATE_ROOM] Error fetching existing host rooms:', fetchHostRoomsError);
        setErrorMp('Error checking for existing rooms. Please try again.');
        return;
      }

      if (existingHostRooms && existingHostRooms.length > 0) {
        console.log(`[Client] [CREATE_ROOM] Found ${existingHostRooms.length} existing rooms to analyze:`, existingHostRooms);

        // Show user-friendly message about cleanup
        const staleRooms = existingHostRooms; // ALL existing rooms are considered stale when host wants to create new one

        // Note: Previous logic was too restrictive - it would only clean up rooms where host was marked as disconnected
        // But if the host is trying to create a new room, they want to abandon ALL previous rooms

        if (staleRooms.length > 0) {
          console.log(`[Client] [CREATE_ROOM] Found ${staleRooms.length} stale rooms to clean up`);
          setErrorMp(`Cleaning up ${staleRooms.length} inactive room(s)...`);
        }

        // Clean up each existing room
        for (const oldRoom of existingHostRooms) {
          console.log(`[Client] [CREATE_ROOM] Analyzing room for cleanup: ${oldRoom.id} (${oldRoom.title || 'Untitled'})`);

          const hostPlayerRecord = oldRoom.game_players?.find(p => p.user_id === user.id);
          const connectedPlayers = oldRoom.game_players?.filter(p => p.is_connected) || [];
          const totalPlayers = oldRoom.game_players?.length || 0;

          // When a host tries to create a new game, they are signaling intent to abandon previous games.
          // A room should be cleaned up if:
          // 1. It's a waiting room (host hasn't committed to this game yet), OR
          // 2. It's an active room where the host is disconnected (host has left), OR
          // 3. ANY room where the host is trying to create a new room (they're abandoning old ones)
          //
          // CRITICAL FIX: If the host is trying to create a NEW game, they're done with ALL old games.
          // The is_connected flag might be stale, so we should treat ALL previous rooms as stale
          // when the host explicitly wants to create a new one.
          const hostIsDisconnected = !hostPlayerRecord || !hostPlayerRecord.is_connected;
          const isStaleRoom = true; // ALWAYS clean up old rooms when host wants to create new game

          console.log(`[Client] [CREATE_ROOM] Room ${oldRoom.id} analysis:`, {
            status: oldRoom.status,
            hasHostPlayerRecord: !!hostPlayerRecord,
            hostIsConnected: hostPlayerRecord?.is_connected,
            hostIsDisconnected,
            totalPlayers,
            connectedPlayers: connectedPlayers.length,
            isStaleRoom,
            staleReason: 'host wants to create new game - abandoning ALL previous rooms'
          });

          if (isStaleRoom) {
            console.log(`[Client] [CREATE_ROOM] Processing room ${oldRoom.id} for host departure...`);

            try {
              if (oldRoom.status === 'waiting' || connectedPlayers.length === 0) {
                // For waiting rooms or rooms with no connected players, delete entirely
                console.log(`[Client] [CREATE_ROOM] Deleting empty/waiting room ${oldRoom.id}...`);

                const { error: deletePlayersError } = await supabase
                  .from('game_players')
                  .delete()
                  .eq('room_id', oldRoom.id);

                if (deletePlayersError) {
                  console.error(`[Client] [CREATE_ROOM] Error deleting players from room ${oldRoom.id}:`, deletePlayersError);
                } else {
                  console.log(`[Client] [CREATE_ROOM] ✅ Deleted all players from room ${oldRoom.id}`);
                }

                const { error: deleteRoomError } = await supabase
                  .from('game_rooms')
                  .delete()
                  .eq('id', oldRoom.id);

                if (deleteRoomError) {
                  console.error(`[Client] [CREATE_ROOM] Error deleting room ${oldRoom.id}:`, deleteRoomError);
                } else {
                  console.log(`[Client] [CREATE_ROOM] ✅ Successfully deleted room ${oldRoom.id}`);
                }

              } else {
                // For active rooms with other connected players, just remove the host
                console.log(`[Client] [CREATE_ROOM] Removing host from active room ${oldRoom.id} with ${connectedPlayers.length} other players...`);

                const { error: removeHostError } = await supabase
                  .from('game_players')
                  .delete()
                  .eq('room_id', oldRoom.id)
                  .eq('user_id', user.id);

                if (removeHostError) {
                  console.error(`[Client] [CREATE_ROOM] Error removing host from room ${oldRoom.id}:`, removeHostError);
                } else {
                  console.log(`[Client] [CREATE_ROOM] ✅ Removed host from room ${oldRoom.id}. Game continues with other players.`);
                }

                // Promote the first connected player to be the new host
                if (connectedPlayers.length > 0) {
                  const newHostId = connectedPlayers[0].user_id;
                  console.log(`[Client] [CREATE_ROOM] Promoting player ${newHostId} to host of room ${oldRoom.id}...`);

                  const { error: promoteHostError } = await supabase
                    .from('game_rooms')
                    .update({ host_id: newHostId })
                    .eq('id', oldRoom.id);

                  if (promoteHostError) {
                    console.error(`[Client] [CREATE_ROOM] Error promoting new host for room ${oldRoom.id}:`, promoteHostError);
                  } else {
                    console.log(`[Client] [CREATE_ROOM] ✅ Promoted player ${newHostId} to host of room ${oldRoom.id}`);
                  }
                }
              }

            } catch (cleanupException) {
              console.error(`[Client] [CREATE_ROOM] Exception processing room ${oldRoom.id}:`, cleanupException);
              // Continue with other rooms
            }
          } 
          // Note: else case removed since isStaleRoom is now always true
        }

        // Refresh the lobby to reflect changes
        console.log(`[Client] [CREATE_ROOM] Refreshing lobby after cleanup...`);
        await fetchAndSetGameRooms();

        // Clear the cleanup message
        setErrorMp(null);
      } else {
        console.log(`[Client] [CREATE_ROOM] No existing rooms found for user ${user.id}`);
      }

      // Step 2: Create the new room
      console.log(`[Client] [CREATE_ROOM] Creating new room...`);
      const roomTitle = `${userProfile?.username || 'Host'}'s Game`;
      const now = new Date().toISOString();
      const newRoomData = {
        host_id: user.id,
        title: roomTitle,
        status: 'waiting' as const,
        multiplayer_mode: 'competitive' as const,
        max_players: 4,
        created_at: now,
        last_activity_timestamp: now, // Initialize activity timestamp for janitor tracking
      };

      console.log('[Client] [CREATE_ROOM] Inserting new room with data:', newRoomData);
      const { data: newRoom, error: createError } = await supabase
        .from('game_rooms')
        .insert(newRoomData)
        .select()
        .single();

      if (createError) {
        console.error('[Client] [CREATE_ROOM] Error creating room:', createError);
        setErrorMp(`Failed to create room: ${createError.message}`);
        return;
      }

      if (!newRoom) {
        console.error('[Client] [CREATE_ROOM] No room data returned from insert');
        setErrorMp('Failed to create room: no data returned.');
        return;
      }

      console.log(`[Client] [CREATE_ROOM] *** ROOM CREATED SUCCESSFULLY *** ${JSON.stringify(newRoom)}`);
      const newRoomId = newRoom.id;

      // Step 3: Auto-join the host to their own room
      console.log(`[Client] [CREATE_ROOM] *** ABOUT TO CALL HANDLEJOINROOM *** with room ID: ${newRoomId}`);
      await handleJoinRoom(newRoomId, true); // true indicates auto-join after create

      console.log(`[Client] [CREATE_ROOM] *** AUTO-JOIN COMPLETED SUCCESSFULLY ***`);

      // THE FIX: Immediately fetch the player list for the new room so the host sees themselves
      console.log('[Client] [CREATE_ROOM] Fetching initial player list for host after auto-join');
      await fetchPlayersInActiveRoom(newRoomId, 'host_create');

      console.log(`[Client] [CREATE_ROOM] *** ROOM CREATION AND HOST AUTO-JOIN FULLY COMPLETED *** Room: ${newRoomId}`);

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      console.error('[Client] [CREATE_ROOM] Exception during room creation:', err);
      setErrorMp(`Exception creating room: ${err.message}`);
    } finally {
      // IMPORTANT: Unset the flag in a finally block
      // This ensures it gets reset even if an error occurs.
      setIsCreatingRoom(false);
    }
  };

  // Add fetchPlayersInActiveRoom function
  const fetchPlayersInActiveRoom = useCallback(async (roomId: string, caller: string = 'unknown') => {
    const currentRoomDetails = gameRooms.find(room => room.id === roomId);
    const isCurrentUserHost = currentRoomDetails?.host_id === user?.id;
    const logPrefix = isCurrentUserHost ? '[RoomView HOST]' : '[RoomView]';
    
    console.log(`${logPrefix} [FETCH TRIGGERED] fetchPlayersInActiveRoom called:`, {
      roomId,
      caller,
      timestamp: new Date().toISOString(),
      currentPlayersCount: playersInRoom.length,
      stackTrace: new Error().stack?.split('\n').slice(1, 4).join(' | '), // Show top 3 stack frames
      triggerContext: {
        isCurrentUserHost,
        userId: user?.id,
        hostId: currentRoomDetails?.host_id,
        currentPlayerIds: playersInRoom.map(p => p.user_id),
        multiplayerPanelState,
        centerPanelMpState
      }
    });

    if (!roomId) {
      console.warn(`${logPrefix} fetchPlayersInActiveRoom: No roomId provided, returning early`);
      return;
    }

    setIsLoadingPlayers(true);
    console.log(`${logPrefix} Starting to fetch players for room: ${roomId} (called by: ${caller})`);
    
    try {
      console.log(`${logPrefix} Executing Supabase query for game_players WITH profiles join (restored after RLS fix)`);
      const { data, error } = await supabase
        .from('game_players')
        .select(`
          user_id,
          is_ready,
          is_connected,
          room_id,
          profiles (
            username
          )
        `)
        .eq('room_id', roomId);

      if (error) {
        console.error(`${logPrefix} Database error fetching players (called by: ${caller}):`, {
          error: JSON.stringify(error, null, 2),
          errorMessage: error.message,
          errorCode: error.code,
          errorDetails: error.details,
          roomId,
          caller
        });
        setPlayersInRoom([]);
        setErrorMp(`Failed to fetch players: ${error.message}`);
      } else {
        console.log(`${logPrefix} Successfully fetched players from database (called by: ${caller}):`, {
          rawData: data,
          playerCount: data?.length || 0,
          roomId,
          caller,
          timestamp: new Date().toISOString()
        });

        // Transform the data to match PlayerInRoom type
        const transformedData: PlayerInRoom[] = (data || []).map((dbPlayer, index) => {
          console.log(`${logPrefix} Transforming player ${index + 1} (WITH profile join, called by: ${caller}):`, {
            rawPlayer: dbPlayer,
            hasProfileData: !!dbPlayer.profiles,
            profileData: dbPlayer.profiles
          });

          // Handle the profiles data from the join
          let usernameFromProfile: string | null = null;
          if (dbPlayer.profiles) {
            if (Array.isArray(dbPlayer.profiles)) {
              // If Supabase returns profiles as an array
              usernameFromProfile = dbPlayer.profiles[0]?.username || null;
              console.log(`${logPrefix} Profile is array, extracted username:`, usernameFromProfile);
            } else {
              // If Supabase returns profiles as an object
              usernameFromProfile = (dbPlayer.profiles as { username: string | null }).username || null;
              console.log(`${logPrefix} Profile is object, extracted username:`, usernameFromProfile);
            }
          } else {
            console.log(`${logPrefix} No profile data for player:`, dbPlayer.user_id);
          }

          const transformedPlayer = {
            user_id: dbPlayer.user_id,
            is_ready: dbPlayer.is_ready,
            is_connected: dbPlayer.is_connected,
            profile: usernameFromProfile ? { username: usernameFromProfile } : null
          };

          console.log(`${logPrefix} Transformed player ${index + 1} (with profile):`, transformedPlayer);
          return transformedPlayer;
        });

        console.log(`${logPrefix} Final transformed players data (called by: ${caller}):`, {
          transformedData,
          playerCount: transformedData.length,
          playersWithUsernames: transformedData.filter(p => p.profile?.username).length,
          playersReady: transformedData.filter(p => p.is_ready).length,
          playersConnected: transformedData.filter(p => p.is_connected).length,
          caller
        });

        // **CRITICAL DEBUG LOGGING** - Log exact data about to be set in state
        console.log(`${logPrefix} [CRITICAL] *** DATA ABOUT TO BE SET BY fetchPlayersInActiveRoom (called by: ${caller}) ***`, {
          roomId,
          isCurrentUserHost,
          currentUserId: user?.id,
          hostId: currentRoomDetails?.host_id,
          caller,
          dataToSet: {
            playerCount: transformedData.length,
            playersDetailed: transformedData.map(p => ({
              user_id: p.user_id,
              username: p.profile?.username || 'NO_USERNAME',
              is_ready: p.is_ready,
              is_connected: p.is_connected,
              hasProfile: !!p.profile
            })),
            userIdsList: transformedData.map(p => p.user_id),
            currentUserInList: transformedData.some(p => p.user_id === user?.id),
            uniqueUserIds: [...new Set(transformedData.map(p => p.user_id))].length === transformedData.length
          },
          previousState: {
            currentPlayersInRoomCount: playersInRoom.length,
            currentPlayerIds: playersInRoom.map(p => p.user_id)
          },
          expectUIToUpdate: transformedData.length !== playersInRoom.length,
          timestamp: new Date().toISOString()
        });

        if (isCurrentUserHost && transformedData.length > 1) {
          console.log(`${logPrefix} [HOST CRITICAL] *** HOST SETTING STATE WITH MULTIPLE PLAYERS (called by: ${caller}) *** This should update host UI:`, {
            caller,
            totalPlayersToSet: transformedData.length,
            hostShouldSeeThesePlayerIds: transformedData.map(p => p.user_id),
            hostShouldSeeTheseUsernames: transformedData.map(p => p.profile?.username || 'NO_USERNAME'),
            UIUpdateExpected: true,
            playerCountShouldChangeTo: transformedData.length,
            previousPlayerCount: playersInRoom.length,
            whatTriggeredThisFetch: caller
          });
        }

        // **CRITICAL: Create a new array instance to ensure React detects the change**
        const dataToSet = [...transformedData]; // Explicitly create new array
        console.log(`${logPrefix} [IMMUTABILITY CHECK] Creating new array instance for setPlayersInRoom (called by: ${caller}):`, {
          originalArrayReference: transformedData,
          newArrayReference: dataToSet,
          isNewInstance: dataToSet !== transformedData,
          arrayContentsMatch: JSON.stringify(dataToSet) === JSON.stringify(transformedData),
          caller,
          timestamp: new Date().toISOString()
        });

        // **ENHANCED: Log the exact data being passed to setPlayersInRoom**
        console.log(`${logPrefix} [SET_STATE] About to call setPlayersInRoom with (called by: ${caller}):`, {
          dataBeingSet: JSON.parse(JSON.stringify(dataToSet)), // Deep copy for logging
          playerCount: dataToSet.length,
          playerUserIds: dataToSet.map(p => p.user_id),
          playerUsernames: dataToSet.map(p => p.profile?.username || 'NO_USERNAME'),
          isCurrentUserHost,
          roomId,
          caller,
          timestamp: new Date().toISOString()
        });

        setPlayersInRoom(dataToSet);
        console.log(`${logPrefix} Updated playersInRoom state successfully (called by: ${caller})`);
      }
    } catch (e) {
      const error = e instanceof Error ? e : new Error(String(e));
      console.error(`${logPrefix} Exception during fetchPlayersInActiveRoom (called by: ${caller}):`, {
        exception: error,
        exceptionMessage: error.message,
        exceptionStack: error.stack,
        roomId,
        caller
      });
      setPlayersInRoom([]);
      setErrorMp(`Exception fetching players: ${error.message}`);
    } finally {
      setIsLoadingPlayers(false);
      console.log(`${logPrefix} fetchPlayersInActiveRoom completed (called by: ${caller}):`, {
        roomId,
        caller,
        timestamp: new Date().toISOString(),
        finalPlayersCount: playersInRoom.length
      });
    }
  }, [user?.id, gameRooms, playersInRoom, multiplayerPanelState, centerPanelMpState]);

  // Add handleToggleReady function with optimistic updates and submission state
  const handleToggleReady = async () => {
    console.log('[RoomView] handleToggleReady called - Starting comprehensive logging');
    console.log('[RoomView] Current state check:', {
      hasUser: !!user,
      userId: user?.id,
      hasActiveRoomId: !!activeRoomId,
      activeRoomId,
      isSubmittingReady,
      playersInRoomCount: playersInRoom.length,
      timestamp: new Date().toISOString()
    });

    // Early exit conditions with detailed logging
    if (!user?.id || !activeRoomId || isSubmittingReady) {
      if (isSubmittingReady) {
        console.log('[RoomView] Already submitting ready state, skipping this call to prevent duplicate submissions');
        return;
      }
      if (!user?.id) {
        console.warn('[RoomView] handleToggleReady: User ID missing, cannot proceed');
        return;
      }
      if (!activeRoomId) {
        console.warn('[RoomView] handleToggleReady: Active room ID missing, cannot proceed');
        return;
      }
      return;
    }

    // Find current player and validate
    const currentPlayerInRoom = playersInRoom.find(p => p.user_id === user.id);
    console.log('[RoomView] Current player lookup:', {
      foundPlayer: !!currentPlayerInRoom,
      currentPlayerData: currentPlayerInRoom,
      allPlayersInRoom: playersInRoom.map(p => ({ user_id: p.user_id, is_ready: p.is_ready, username: p.profile?.username }))
    });

    if (!currentPlayerInRoom) {
      console.error('[RoomView] Current player not found in playersInRoom list - this should not happen');
      console.error('[RoomView] Debug info:', {
        searchingForUserId: user.id,
        playersInRoom: playersInRoom,
        playersInRoomUserIds: playersInRoom.map(p => p.user_id)
      });
      return;
    }

    // Determine new ready state
    const currentReadyState = currentPlayerInRoom.is_ready;
    const newReadyState = !currentReadyState;
    
    console.log('[RoomView] Ready state transition:', {
      currentReadyState,
      newReadyState,
      playerUserId: user.id,
      roomId: activeRoomId
    });

    // Set submitting state to prevent multiple calls
    setIsSubmittingReady(true);
    console.log('[RoomView] Set isSubmittingReady to true - blocking further submissions');

    // 1. Optimistic UI Update
    console.log('[RoomView] Applying optimistic UI update');
    setPlayersInRoom(prevPlayers => {
      const updatedPlayers = prevPlayers.map(p =>
        p.user_id === user.id ? { ...p, is_ready: newReadyState } : p
      );
      console.log('[RoomView] Optimistic update applied:', {
        beforeUpdate: prevPlayers.find(p => p.user_id === user.id),
        afterUpdate: updatedPlayers.find(p => p.user_id === user.id),
        allPlayersAfterUpdate: updatedPlayers.map(p => ({ user_id: p.user_id, is_ready: p.is_ready }))
      });
      return updatedPlayers;
    });

    try {
      console.log(`[RoomView] Starting database update - attempting to update ready state to: ${newReadyState} for user ${user.id} in room ${activeRoomId}`);
      
      const { data: updatedGamePlayer, error } = await supabase
        .from('game_players')
        .update({ is_ready: newReadyState })
        .eq('user_id', user.id)
        .eq('room_id', activeRoomId)
        .select()
        .single();

      if (error) {
        console.error('[RoomView] Database update failed:', {
          error: JSON.stringify(error, null, 2),
          errorMessage: error.message,
          errorCode: error.code,
          errorDetails: error.details,
          attemptedUpdate: { user_id: user.id, room_id: activeRoomId, is_ready: newReadyState }
        });
        
        // Revert optimistic update on error
        console.log('[RoomView] Reverting optimistic update due to database error');
        setPlayersInRoom(prevPlayers => {
          const revertedPlayers = prevPlayers.map(p =>
            p.user_id === user.id ? { ...p, is_ready: currentReadyState } : p
          );
          console.log('[RoomView] Optimistic update reverted:', {
            revertedTo: currentReadyState,
            playerAfterRevert: revertedPlayers.find(p => p.user_id === user.id)
          });
          return revertedPlayers;
        });
        
        setErrorMp(`Failed to update ready state: ${error.message}`);
      } else {
        console.log('[RoomView] Database update successful:', {
          updatedGamePlayer,
          confirmedReadyState: updatedGamePlayer?.is_ready,
          updateTimestamp: new Date().toISOString()
        });

        // ✅ ROBUST APPROACH: Trust the successful database write
        // The optimistic update is now the confirmed state. No need to wait for realtime.
        // Realtime's job is to inform us of OTHER users' actions, not our own.
        console.log('[RoomView] Database write succeeded - optimistic state is now confirmed state');

        // Clear any previous errors since the operation was successful
        setErrorMp(null);
      }
    } catch (e) {
      const error = e instanceof Error ? e : new Error(String(e));
      console.error('[RoomView] Exception during ready state update:', {
        exception: error,
        exceptionMessage: error.message,
        exceptionStack: error.stack,
        attemptedUpdate: { user_id: user.id, room_id: activeRoomId, is_ready: newReadyState }
      });

      // Revert optimistic update on exception
      console.log('[RoomView] Reverting optimistic update due to exception');
      setPlayersInRoom(prevPlayers => {
        const revertedPlayers = prevPlayers.map(p =>
          p.user_id === user.id ? { ...p, is_ready: currentReadyState } : p
        );
        console.log('[RoomView] Optimistic update reverted after exception:', {
          revertedTo: currentReadyState,
          playerAfterRevert: revertedPlayers.find(p => p.user_id === user.id)
        });
        return revertedPlayers;
      });

      setErrorMp(`Exception updating ready state: ${error.message}`);
    } finally {
      setIsSubmittingReady(false);
      console.log('[RoomView] Set isSubmittingReady to false - ready for next submission');
      console.log('[RoomView] handleToggleReady completed at:', new Date().toISOString());
    }
  };

  // FINAL EFFECT 1: Manages all Realtime Subscriptions
  useEffect(() => {
    // If the user isn't logged in, do nothing.
    if (!user?.id) {
      return;
    }

    let channels: RealtimeChannel[] = [];

    if (activeRoomId) {
      // --- IN-ROOM LOGIC ---
      console.log(`[Realtime] ✅ Setting up SPECIFIC subscriptions and heartbeat for room: ${activeRoomId}`);

      const roomChannel = supabase.channel(`specific_room-${activeRoomId}`);
      const playersChannel = supabase.channel(`room-${activeRoomId}-players`);

      roomChannel
        .on('postgres_changes', { event: '*', schema: 'public', table: 'game_rooms', filter: `id=eq.${activeRoomId}` }, (payload) => {
          console.log(`[Realtime] Specific room update for ${activeRoomId}`, payload.new);
          setCurrentRoomGameData(payload.new as GameRoom);
        })
        .subscribe((status, err) => {
          if (err) console.error(`[Realtime] ❌ Error on specific_room channel for ${activeRoomId}`, err);
        });

      playersChannel
        .on('postgres_changes', { event: '*', schema: 'public', table: 'game_players', filter: `room_id=eq.${activeRoomId}` }, () => {
          fetchPlayersInActiveRoom(activeRoomId, 'realtime_player_update');
        })
        .subscribe((status, err) => {
          if (err) console.error(`[Realtime] ❌ Error on room-players channel for ${activeRoomId}`, err);
        });

      channels = [roomChannel, playersChannel];

    } else {
      // --- LOBBY LOGIC ---
      console.log('[Realtime] ✅ Setting up GLOBAL subscriptions for user in lobby');

      const globalRoomsChannel = supabase.channel('global-room-updates');

      globalRoomsChannel
        .on('postgres_changes', { event: '*', schema: 'public', table: 'game_rooms' }, () => fetchAndSetGameRooms())
        .subscribe((status, err) => {
          if (err) console.error('[Realtime] ❌ Error subscribing to global game_rooms changes:', err);
        });

      channels = [globalRoomsChannel];
    }

    return () => {
      console.log(`[Realtime] Cleaning up ${channels.length} channels.`);
      channels.forEach(channel => supabase.removeChannel(channel));
    };
  }, [user?.id, activeRoomId, fetchAndSetGameRooms, fetchPlayersInActiveRoom]); // Include data fetching callbacks

  // FINAL EFFECT 2: Manages Heartbeat
  useEffect(() => {
    // Only run the heartbeat if the user is in a room.
    if (user?.id && activeRoomId) {
      const heartbeat = async () => {
        console.log(`[HEARTBEAT] Sending for room ${activeRoomId}`);
        try {
          const { error } = await supabase.functions.invoke('heartbeat-handler', {
            body: { roomId: activeRoomId, action: 'ping' }
          });
          if (error) {
            console.error('[HEARTBEAT] Error invoking heartbeat-handler:', error);
          }
        } catch (err) {
          console.error('[HEARTBEAT] Exception invoking heartbeat-handler:', err);
        }
      };

      heartbeat(); // Send one immediately on joining
      const intervalId = setInterval(heartbeat, 15000);

      return () => clearInterval(intervalId);
    }
  }, [user?.id, activeRoomId]);

  // FINAL EFFECT 3: Manages Tab Visibility
  useEffect(() => {
    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'visible') {
        console.log('[DISCONNECT_DETECTION] Page became visible - forcing data resync.');
        supabase.realtime.connect();

        if (activeRoomId) {
          console.log('[DISCONNECT_DETECTION] Silently refreshing full room state.');
          const { data: roomData } = await supabase.from('game_rooms').select('*').eq('id', activeRoomId).single();
          if (roomData) setCurrentRoomGameData(roomData);
          await fetchPlayersInActiveRoom(activeRoomId, 'visibility_change');
        }
      }
    };

    const visibilityWrapper = () => {
      handleVisibilityChange().catch(err =>
        console.warn(
          '[DISCONNECT_DETECTION] final visibility handler rejected:',
          err instanceof Error ? err.message : err
        )
      );
    };
    document.addEventListener('visibilitychange', visibilityWrapper);
    return () => document.removeEventListener('visibilitychange', visibilityWrapper);
  }, [activeRoomId, fetchPlayersInActiveRoom]); // Include player fetch callback

  // Update handleStartGame function after handleToggleReady
  const handleStartGame = async () => {
    // CRITICAL: Early exit with detailed logging if already starting
    if (isStartingGame) {
      console.warn("[Client] Start game request already in progress - blocking duplicate request.");
      console.log("[Client] [RACE_CONDITION_PREVENTION] isStartingGame flag prevented duplicate start game call", {
        timestamp: new Date().toISOString(),
        activeRoomId,
        userId: user?.id,
        currentFlag: isStartingGame
      });
      return;
    }

    console.log("[Client] [START_GAME_ATTEMPT] Beginning start game process", {
      timestamp: new Date().toISOString(),
      activeRoomId,
      userId: user?.id,
      userIsHost: user && gameRooms.find(room => room.id === activeRoomId)?.host_id === user.id,
      playersInRoomCount: playersInRoom.length,
      allPlayersReady: playersInRoom.length > 0 && playersInRoom.every(p => p.is_ready),
      currentRoomStatus: gameRooms.find(room => room.id === activeRoomId)?.status
    });

    // CRITICAL: Ensure you have the LATEST room details and current user
    if (!activeRoomId || !user || !user.id) {
      console.error("[Client] Start game conditions not met: Missing user or room information.", {
        activeRoomId: !!activeRoomId,
        user: !!user,
        userId: !!user?.id
      });
      setErrorMp("Cannot start game: Missing user or room information.");
      return;
    }

    // CRITICAL FIX: Fetch fresh room details from server instead of relying on potentially stale gameRooms array
    console.log("[Client] [START_GAME] Fetching latest room details from server before host check...");
    let currentRoomDetails;
    try {
      const { data: freshRoomData, error: roomFetchError } = await supabase
        .from('game_rooms')
        .select('*')
        .eq('id', activeRoomId)
        .single();

      if (roomFetchError || !freshRoomData) {
        console.error("[Client] Start game conditions not met: Failed to fetch current room details", roomFetchError);
        setErrorMp("Failed to fetch room details. Cannot start game.");
        return;
      }

      currentRoomDetails = freshRoomData;
      console.log("[Client] [START_GAME] Fresh room data fetched:", {
        roomId: activeRoomId,
        status: currentRoomDetails.status,
        hostId: currentRoomDetails.host_id
      });
    } catch (fetchError) {
      console.error("[Client] Exception fetching room details:", fetchError);
      setErrorMp("Exception fetching room details. Cannot start game.");
      return;
    }

    // CRITICAL: Double-check host status with latest data
    const isCurrentUserHost = currentRoomDetails.host_id === user.id;
    console.log("[Client] [START_GAME] Host verification check:", {
      currentUserId: user.id,
      roomHostId: currentRoomDetails.host_id,
      isCurrentUserHost,
      roomStatus: currentRoomDetails.status
    });

    if (!isCurrentUserHost) {
      console.error("[Client] Start game conditions not met: Not the host.", { 
        hostId: currentRoomDetails.host_id, 
        currentUserId: user.id,
        roomId: activeRoomId
      });
      setErrorMp("Only the host can start the game.");
      return;
    }

    // Validate game can be started
    const allPlayersReady = playersInRoom.length > 0 && playersInRoom.every(p => p.is_ready);
    const minPlayersMet = playersInRoom.length >= 2; // Keep requirement of 2 players
    const roomIsWaiting = currentRoomDetails.status === 'waiting';

    console.log("[Client] [START_GAME] Pre-flight validation:", {
      allPlayersReady,
      minPlayersMet,
      roomIsWaiting,
      playerCount: playersInRoom.length,
      roomStatus: currentRoomDetails.status
    });

    // CRITICAL FIX: Handle case where game has already started (state synchronization)
    if (!roomIsWaiting) {
      console.warn(`[Client] [STATE_SYNC_FIX] Attempted to start a game that is not in 'waiting' status (current: ${currentRoomDetails.status}). Forcing state synchronization.`);

      // The game is already active. The client is out of sync.
      // Force the client to catch up to the correct state.
      setCurrentRoomGameData(currentRoomDetails); // Update state with the correct room data

      // Also update the gameRooms array to keep it in sync
      setGameRooms(prevRooms =>
        prevRooms.map(room => room.id === activeRoomId ? { ...room, ...currentRoomDetails } : room)
      );

      // The UI will now re-render based on the correct status,
      // which should show the game view instead of the lobby.
      console.log(`[Client] [STATE_SYNC_FIX] State synchronization complete. UI should now reflect status: ${currentRoomDetails.status}`);

      setIsStartingGame(false); // Make sure to clear the loading state
      return; // Stop the function here
    }

    if (!minPlayersMet) {
      console.warn("[Client] Minimum players not met.", {
        playerCount: playersInRoom.length,
        required: 2
      });
      setErrorMp("Cannot start game: minimum 2 players required.");
      return;
    }

    if (!allPlayersReady) {
      console.warn("[Client] Not all players are ready.", {
        totalPlayers: playersInRoom.length,
        readyPlayers: playersInRoom.filter(p => p.is_ready).length
      });
      setErrorMp("Cannot start game: all players must be ready.");
      return;
    }

    // CRITICAL: Set flag IMMEDIATELY before starting and clear any previous errors
    console.log(`[Client] Host ${user.id} attempting to start game in room ${activeRoomId} - setting isStartingGame to TRUE`);
    setIsStartingGame(true);
    setErrorMp(null); // Clear any previous errors

    try {
      console.log(`[Client] [START_GAME_EDGE_CALL] Invoking start-game-handler for room ${activeRoomId}`);
      const { data, error: invokeError } = await supabase.functions.invoke('start-game-handler', {
        body: { roomId: activeRoomId },
      });

      if (invokeError) {
        console.error("[Client] Error invoking start-game-handler:", invokeError);

        // Enhanced error handling for Edge Function responses
        let errorMessage = 'Failed to start game.';
        let shouldTransitionToGame = false;

        // Try to extract detailed error information from the server response
        try {
          if (invokeError.context && invokeError.context.json) {
            const errorData = invokeError.context.json;
            errorMessage = errorData.error || errorData.message || invokeError.message;

            // Handle specific conflict scenarios where game was actually started
            if (errorData.conflictType === 'RACE_CONDITION_ALREADY_STARTED' ||
                errorData.conflictType === 'CONCURRENT_START_ATTEMPT' ||
                (errorData.currentStatus === 'active' && errorData.gameStartTime)) {
              console.log("[Client] [START_GAME_409_HANDLING] Game was already started by concurrent request", {
                conflictType: errorData.conflictType,
                currentStatus: errorData.currentStatus,
                gameStartTime: errorData.gameStartTime,
                suggestion: errorData.suggestion
              });

              // Since the game was actually started (just not by this request), transition to active game
              shouldTransitionToGame = true;
              errorMessage = "Game started successfully (by concurrent request)";
            }

            // Handle specific validation errors with helpful messages
            if (errorData.error === 'Not all players are ready.') {
              errorMessage = `Cannot start game: ${errorData.details || 'Not all players are ready'}`;
              console.log("[Client] [START_GAME_VALIDATION] Player readiness validation failed:", {
                readyCount: errorData.readyCount,
                totalCount: errorData.totalCount,
                details: errorData.details
              });
            } else if (errorData.error === 'Minimum players not met.') {
              errorMessage = `Cannot start game: ${errorData.details || 'Need at least 2 players'}`;
              console.log("[Client] [START_GAME_VALIDATION] Minimum players validation failed:", {
                currentCount: errorData.currentCount,
                requiredCount: errorData.requiredCount,
                details: errorData.details
              });
            }
          } else if (invokeError.message) {
            errorMessage = invokeError.message;
          }
        } catch (parseError) {
          console.warn("[Client] Failed to parse error response, using fallback message:", parseError);
          errorMessage = invokeError.message || 'Failed to start game - server error';
        }

        if (shouldTransitionToGame) {
          console.log("[Client] [START_GAME_RECOVERY] Transitioning to active game despite 409 error");
          // Don't set error - the game is actually started
          // Realtime should handle the state update, but we can also trigger a refresh
          await fetchAndSetGameRooms(); // Refresh room data
        } else {
          console.error("[Client] [START_GAME_ERROR] Setting error message:", errorMessage);
          setErrorMp(errorMessage);
        }
      } else {
        console.log("[Client] [START_GAME_SUCCESS] Successfully invoked start-game-handler:", data);

        // --- CRITICAL FIX: Optimistic UI Update ---
        // Don't wait for the realtime event. The call was successful, so we know the game has started.
        // Update the local state immediately to make the UI feel instantaneous.
        console.log('[Client] [OPTIMISTIC_UPDATE] Forcing UI transition to active game state.');
        setCurrentRoomGameData(prevData => {
          if (!prevData) return null; // Should not happen here

          // Extract relevant info from Edge Function response
          const {
            firstQuestion,
            gameStartTime,
            roundEndsAt,
            roundNumber
          } = (data || {}) as {
            firstQuestion?: PlayerQuestion;
            gameStartTime?: string;
            roundEndsAt?: string;
            roundNumber?: number;
          };

          return {
            ...prevData,
            status: 'active',
            current_question_data: firstQuestion ?? prevData.current_question_data,
            current_round_number: roundNumber ?? prevData.current_round_number,
            current_round_answers: [],
            game_start_timestamp: gameStartTime ?? prevData.game_start_timestamp,
            current_round_ends_at: roundEndsAt ?? prevData.current_round_ends_at
          } as GameRoom;
        });
        // --- END OF OPTIMISTIC UPDATE FIX ---

        // Keep the lobby fetch for general list updates, but it's no longer
        // critical for the host's own UI transition.
        await fetchAndSetGameRooms();
      }
    } catch (e) {
      const error = e instanceof Error ? e : new Error(String(e));
      console.error("[Client] Client-side exception invoking start-game-handler:", error);
      setErrorMp(`Client-side exception: ${error.message}`);
    } finally {
      // CRITICAL: Always clear the flag in finally block
      console.log("[Client] [START_GAME_CLEANUP] Clearing isStartingGame flag");
      setIsStartingGame(false);
    }
  };

  // Add handleMultiplayerAnswerSubmit function after handleStartGame
  const handleMultiplayerAnswerSubmit = async (choiceName: string) => {
    // Guards: Don't submit if no user, no room, or answer already submitted for this question
    if (!user || !activeRoomId || submittedAnswerForQuestion) {
      return;
    }

    // Immediately update UI state to prevent double-submission
    setSubmittedAnswerForQuestion(choiceName);

    console.log(`[Client] User ${user.id} submitting answer "${choiceName}" for room ${activeRoomId}`);

    // --- OPTIMISTIC UI UPDATE ---
    // WHY: We immediately add the current user to the list of submitted players
    // in the LOCAL state. This makes the UI feel instant for the person answering.
    const questionData = currentRoomGameData?.current_question_data;
    const selectedChoice = questionData?.choices.find(choice => choice.name === choiceName);

    if (selectedChoice && questionData) {
      const optimisticAnswerData = {
        userId: user.id,
        questionId: questionData.questionId,
        choiceName: choiceName,
        timestamp: Date.now(),
        isCorrect: selectedChoice.isCorrect,
        isPending: true
      };

      console.log('[OPTIMISTIC_ANSWER_DEBUG] Setting optimistic answer:', {
        choiceName,
        selectedChoiceIsCorrect: selectedChoice.isCorrect,
        optimisticAnswerData,
        questionChoices: questionData.choices.map(c => ({ name: c.name, isCorrect: c.isCorrect }))
      });

      // Use flushSync to force immediate synchronous rendering
      flushSync(() => {
        setOptimisticAnswer(optimisticAnswerData);
        setHasSubmittedCurrentRound(true); // Prevent double submission
      });
    }
    // --- END OF OPTIMISTIC UPDATE ---

    setIsSubmittingAnswer(true);

    try {
      const { error } = await supabase.functions.invoke('submit-answer-handler', {
        body: { roomId: activeRoomId, choiceName: choiceName },
      });

      if (error) {
        // The function handles conflicts (409) and other issues.
        throw new Error(error.message);
      }
      console.log('[Client] Answer submitted successfully.');

    } catch (err) {
      console.error('[Client] Error submitting answer:', err);
      // If it fails, revert the optimistic update and allow the user to try again.
      setSubmittedAnswerForQuestion(null);
      setOptimisticAnswer(null);
      setHasSubmittedCurrentRound(false);
      // You should show an error toast to the user here.
    } finally {
      setIsSubmittingAnswer(false);
    }
  };

  // Add effect to reset submission state when question changes and check existing answers
  useEffect(() => {
    if (currentRoomGameData?.current_question_data?.questionId && user) {
      const currentQuestionId = currentRoomGameData.current_question_data.questionId;

      // When a NEW question arrives, RESET this state.
      console.log('[Client] New question detected, resetting submission state');
      setSubmittedAnswerForQuestion(null);

      // Check if user has already submitted an answer for this question
      const currentAnswers: GameAnswer[] = Array.isArray(currentRoomGameData.current_round_answers)
        ? currentRoomGameData.current_round_answers
        : [];

      const userAnswer = currentAnswers.find(answer =>
        answer.userId === user.id && answer.questionId === currentQuestionId
      );

      if (userAnswer) {
        // User has already submitted for this question
        setHasSubmittedCurrentRound(true);
        console.log('[Client] User has already submitted answer for current question:', userAnswer.choiceName);

        // Clear optimistic answer if real-time answer arrived
          if (optimisticAnswer && optimisticAnswer.userId === user.id && optimisticAnswer.questionId === currentQuestionId) {
            console.log('[Client] Real-time answer confirmed, clearing optimistic answer');
            setOptimisticAnswer(null);
          }
      } else {
        // Reset submission state for new question
        setHasSubmittedCurrentRound(false);
        // FIX: Reset hasAnswered state for new question
        setHasAnswered(false);
        console.log('[Client] New question detected, resetting submission state');

        // Clear optimistic answer for new question
        if (optimisticAnswer && optimisticAnswer.questionId !== currentQuestionId) {
          console.log('[Client] New question detected, clearing stale optimistic answer');
          setOptimisticAnswer(null);
        }
      }
    }
  }, [currentRoomGameData?.current_question_data?.questionId, currentRoomGameData?.current_round_answers, user, user?.id, optimisticAnswer]);



  // Stabilized position object to prevent infinite loops in GlobalMultiplayerScoreAnimation
  const stableAnimationPosition = useMemo(() => {
    // Only create this when window is available to avoid SSR issues
    if (typeof window === 'undefined') return { x: 0, y: 0 };
    return { x: window.innerWidth * 0.8, y: window.innerHeight * 0.5 };
  }, []); // Empty dependency array - position is fixed

  // CONSOLIDATED Effect to handle both animation tracking AND triggering for multiplayer answers
  useEffect(() => {
    if (
      selectedOverallGameType !== 'multiplayer' ||
      !currentRoomGameData ||
      currentRoomGameData.status !== 'active' ||
      !currentRoomGameData.current_round_answers ||
      !currentRoomGameData.current_question_data?.questionId
    ) {
      console.log('[FOOTBALL_DEBUG] Animation trigger skipped - conditions not met:', {
        gameType: selectedOverallGameType,
        hasRoomData: !!currentRoomGameData,
        status: currentRoomGameData?.status,
        hasAnswers: !!currentRoomGameData?.current_round_answers,
        hasQuestionId: !!currentRoomGameData?.current_question_data?.questionId
      });
      return;
    }

    const currentQuestionId = currentRoomGameData.current_question_data.questionId;
    console.log('[FOOTBALL_DEBUG] Processing animations for question:', currentQuestionId, {
      answersCount: currentRoomGameData.current_round_answers.length,
      triggeredAnimationsCount: triggeredAnimationsRef.current.size
    });

    // Clear triggered animations when question changes
    if (triggeredAnimationsRef.current.size > 0) {
      const shouldClear = [...triggeredAnimationsRef.current].some((id: string) =>
        !id.includes(currentQuestionId)
      );
      if (shouldClear) {
        console.log('[FOOTBALL_DEBUG] Clearing triggered animations for new question');
        triggeredAnimationsRef.current.clear();
      }
    }

    // Process answers for IMMEDIATE animation triggering (no waiting for state updates)
    currentRoomGameData.current_round_answers.forEach((answer) => {
      if (!answer.isCorrect || answer.questionId !== currentQuestionId) {
        return;
      }

      // Create a stable unique ID based on the answer data (no Date.now() to prevent infinite loops)
      const uniqueAnimationId = `${answer.userId}-${answer.questionId}-${answer.timestamp}`;

      // Skip if already triggered
      if (triggeredAnimationsRef.current.has(uniqueAnimationId)) {
        return;
      }

      console.log('[FOOTBALL_DEBUG] Processing NEW correct answer for animation:', {
        userId: answer.userId.substring(0, 8) + '...',
        isCorrect: answer.isCorrect,
        questionId: answer.questionId,
        uniqueAnimationId
      });

      // Mark as triggered IMMEDIATELY
      triggeredAnimationsRef.current.add(uniqueAnimationId);

      // Get bonus levels and calculate score
      const playerBonusLevels = currentRoomGameData.player_bonus_levels || {};
      const bonusLevel = playerBonusLevels[answer.userId] || 0;
      let scoreIncrease = 10; // Base score

      // Calculate bonus based on consecutive correct answers (BQ system)
      if (bonusLevel === 1) {
        scoreIncrease += 5; // BQ1: +5 bonus
      } else if (bonusLevel === 2) {
        scoreIncrease += 10; // BQ2: +5+5 bonus (stacking)
      } else if (bonusLevel >= 3) {
        scoreIncrease += 15; // BQ3+: +5+10 bonus (stacking)
      }

      // Trigger single-player animation for current user
      if (answer.userId === user?.id) {
        useGameStore.setState((state) => ({
          lastScoreChange: scoreIncrease,
          animationTrigger: state.animationTrigger + 1,
          streak: bonusLevel, // Use bonus level as streak for football count
        }));
      }

      console.log('[FOOTBALL_DEBUG] Triggering ENHANCED animation:', uniqueAnimationId, {
        scoreIncrease,
        bonusLevel,
        position: stableAnimationPosition,
        userId: answer.userId.substring(0, 8) + '...'
      });

      setGlobalAnimations(prev => [...prev, {
        id: uniqueAnimationId, // Use the unique ID here
        type: 'enhanced',
        trigger: Date.now() + Math.random() * 1000, // Ensure unique trigger values
        scoreIncrease,
        bonusLevel,
        originPosition: stableAnimationPosition // Use the stable position object
      }]);

      // Clean up animation after duration
      setTimeout(() => {
        setGlobalAnimations(prev => prev.filter(anim => anim.id !== uniqueAnimationId));
      }, 3000);
    });
  }, [
    selectedOverallGameType,
    currentRoomGameData?.current_round_answers,
    currentRoomGameData?.current_question_data?.questionId,
    currentRoomGameData?.status,
    currentRoomGameData?.player_bonus_levels,
    user?.id,
    currentRoomGameData,
    stableAnimationPosition
    // Removed getElementPosition and stableAnimationPosition to prevent infinite loops
  ]);

  // Add new useEffect for debugging player scores panel
  useEffect(() => {
    if (currentRoomGameData?.status === 'active' && multiplayerPanelState === 'in_room') {
      console.log("[PlayerScoresPanel] Rendering with playersInRoom:", playersInRoom);
    }
  }, [playersInRoom, currentRoomGameData?.status, multiplayerPanelState, currentRoomGameData]);

  // Add this to window object for easy debugging in browser console
  if (typeof window !== 'undefined') {
    // ENHANCED: Add the new sign-out handler to window for debugging
    (window as { debugEnhancedSignOut?: () => Promise<void> }).debugEnhancedSignOut = handleSignOut;
    (window as { debugCurrentStates?: () => object }).debugCurrentStates = () => {
      return {
        activeRoomId,
        selectedRoomForDetail,
        playersInRoom: playersInRoom.length,
        multiplayerPanelState,
        centerPanelMpState,
        gameRooms: gameRooms.length,
        user: user?.id,
        timestamp: new Date().toISOString()
      };
    };
    
    // DEBUG: Enhanced room/player debugging functions
    (window as { debugCheckRoomCleanup?: (roomId?: string) => Promise<unknown> }).debugCheckRoomCleanup = async (roomId?: string) => {
      const targetRoomId = roomId || activeRoomId;
      if (!targetRoomId) {
        console.log('[DEBUG] No room ID provided and no active room');
        return;
      }
      
      console.log(`[DEBUG] Checking cleanup status for room: ${targetRoomId}`);
      
      // Check if room still exists
      const { data: roomData, error: roomError } = await supabase
        .from('game_rooms')
        .select('*')
        .eq('id', targetRoomId)
        .maybeSingle();
        
      console.log('[DEBUG] Room existence check:', { roomData, roomError });
      
      // Check game_players for this room
      const { data: playersData, error: playersError } = await supabase
        .from('game_players')
        .select('*')
        .eq('room_id', targetRoomId);
        
      console.log('[DEBUG] Players in room check:', { playersData, playersError });
      
      return {
        roomExists: !!roomData,
        roomData,
        playersInDb: playersData || [],
        summary: {
          roomDeleted: !roomData,
          playerCount: playersData?.length || 0,
          connectedPlayers: playersData?.filter(p => p.is_connected).length || 0,
          userStillInRoom: playersData?.some(p => p.user_id === user?.id) || false
        }
      };
    };
    
    // DEBUG: Test leave-room-handler directly
    (window as { debugTestLeaveRoomHandler?: (roomId?: string) => Promise<unknown> }).debugTestLeaveRoomHandler = async (roomId?: string) => {
      const targetRoomId = roomId || activeRoomId;
      if (!targetRoomId) {
        console.log('[DEBUG] No room ID provided and no active room');
        return;
      }
      
      console.log(`[DEBUG] Testing leave-room-handler for room: ${targetRoomId}`);
      
      try {
        const { data, error } = await supabase.functions.invoke('leave-room-handler', {
          body: { roomId: targetRoomId },
        });
        
        console.log('[DEBUG] leave-room-handler response:', { data, error });
        return { data, error };
      } catch (e) {
        console.error('[DEBUG] leave-room-handler exception:', e);
        return { exception: e };
      }
    };
    
    // DEBUG: Comprehensive state inspector
    (window as { debugInspectAllStates?: () => object }).debugInspectAllStates = () => {
      const states = {
        authentication: {
          user: user?.id,
          userProfile: userProfile?.username,
          loadingUser,
          loadingProfile
        },
        multiplayer: {
          selectedOverallGameType,
          multiplayerPanelState,
          centerPanelMpState,
          activeRoomId,
          selectedRoomForDetail: selectedRoomForDetail?.id,
          currentRoomGameData: currentRoomGameData?.id,
          playersInRoom: playersInRoom.length,
          gameRooms: gameRooms.length
        },
        flags: {
          isLoadingRooms,
          isCreatingRoom,
          isStartingGame,
          isSubmittingReady,
          isLeavingRoom,
          isJoiningOrRejoiningRoom,
          isCreatingAndJoiningRoom
        },
        errors: {
          errorMp,
          lobbyFetchError
        },
        timestamp: new Date().toISOString()
      };
      
      console.log('[DEBUG] Complete state inspection:', states);
      return states;
    };

    // **NEW**: Debug function to manually trigger fetchAndSetGameRooms
    (window as { debugFetchRooms?: () => Promise<unknown> }).debugFetchRooms = async () => {
      console.log('[DEBUG] Manually triggering fetchAndSetGameRooms...');
      try {
        await fetchAndSetGameRooms();
        console.log('[DEBUG] fetchAndSetGameRooms completed successfully');
        return { success: true, gameRoomsCount: gameRooms.length };
      } catch (error) {
        console.error('[DEBUG] fetchAndSetGameRooms failed:', error);
        return { success: false, error };
      }
    };

    // **NEW**: Debug function to inspect current query being used
    (window as { debugQueryInfo?: () => object }).debugQueryInfo = () => {
      return {
        userContext: user?.id,
        userAuthenticated: !!user,
        currentFilters: {
          table: 'game_rooms',
          statusIn: ['waiting', 'active'],
          aggregationMethod: 'game_players(count)',
          orderBy: 'created_at desc'
        },
        expectedBehavior: 'Should return ALL waiting/active rooms that RLS permits with correct player counts',
        troubleshootingHint: 'If returning 0 counts, check game_players RLS policies for count aggregation'
      };
    };

    // **NEW**: Debug function to test connection recovery
    (window as { debugConnectionRecovery?: () => object }).debugConnectionRecovery = () => {
      return {
        currentConnectionStatus: connectionStatus,
        activeRoomId,
        multiplayerPanelState,
        playersInRoom: playersInRoom.length,
        isOnline,
        actions: {
          simulateReconnecting: () => {
            console.log('[DEBUG] Simulating RECONNECTING state...');
            setConnectionStatus('RECONNECTING');
          },
          simulateConnected: () => {
            console.log('[DEBUG] Simulating CONNECTED state...');
            setConnectionStatus('CONNECTED');
          },
          simulateOffline: () => {
            console.log('[DEBUG] Simulating OFFLINE state...');
            setConnectionStatus('OFFLINE');
          },
          triggerRecovery: () => {
            console.log('[DEBUG] Triggering manual recovery attempt...');
            if (activeRoomId) {
              fetchPlayersInActiveRoom(activeRoomId, 'debug_manual_recovery');
            } else {
              console.warn('[DEBUG] No active room to recover');
            }
          }
        }
      };
    };

    // **NEW**: Debug function to test game_players RLS directly
    (window as { debugTestGamePlayersRLS?: (roomId?: string) => Promise<unknown> }).debugTestGamePlayersRLS = async (roomId?: string) => {
      const targetRoomId = roomId || 'af7d98c9-c273-4ca1-a7ae-17821942bed0'; // fresh's room from screenshot
      
      console.log(`[DEBUG] Testing game_players RLS for room: ${targetRoomId}`);
      console.log(`[DEBUG] Current user: ${user?.id}`);
      
      try {
        // Test 1: Can we see individual game_players records?
        console.log('[DEBUG] Test 1: Fetching individual game_players records...');
        const { data: playersData, error: playersError } = await supabase
          .from('game_players')
          .select('user_id, is_connected, is_ready')
          .eq('room_id', targetRoomId);
        
        console.log('[DEBUG] Test 1 Results:', { 
          playersData, 
          playersError,
          playerCount: playersData?.length || 0
        });
        
        // Test 2: Can we get count aggregation?
        console.log('[DEBUG] Test 2: Testing count aggregation...');
        const { data: countData, error: countError } = await supabase
          .from('game_players')
          .select('*', { count: 'exact', head: true })
          .eq('room_id', targetRoomId);
        
        console.log('[DEBUG] Test 2 Results:', { 
          countData, 
          countError,
          count: countData // This should be null but the count is in the response metadata
        });
        
        // Test 3: Can we use count in a join context like fetchAndSetGameRooms?
        console.log('[DEBUG] Test 3: Testing count in join context...');
        const { data: joinData, error: joinError } = await supabase
          .from('game_rooms')
          .select(`
            id,
            title,
            status,
            game_players(count)
          `)
          .eq('id', targetRoomId)
          .maybeSingle();
        
        console.log('[DEBUG] Test 3 Results:', { 
          joinData, 
          joinError,
          gamePlayersStructure: joinData?.game_players,
          extractedCount: Array.isArray(joinData?.game_players) && joinData.game_players[0]?.count
        });
        
        return {
          test1_individualRecords: { playersData, playersError, count: playersData?.length || 0 },
          test2_countAggregation: { countData, countError },
          test3_joinContext: { joinData, joinError, extractedCount: Array.isArray(joinData?.game_players) && joinData.game_players[0]?.count },
          summary: {
            canSeeIndividualRecords: !playersError && Array.isArray(playersData),
            individualRecordCount: playersData?.length || 0,
            canUseCountInJoin: !joinError && joinData?.game_players,
            joinExtractedCount: Array.isArray(joinData?.game_players) && joinData.game_players[0]?.count || 0,
            possibleRLSIssue: playersError || joinError || (playersData?.length === 0 && user?.id !== targetRoomId) // Simplified check
          }
        };
        
      } catch (e) {
        console.error('[DEBUG] Exception testing game_players RLS:', e);
        return { exception: e };
      }
    };

    // **NEW**: Debug function to compare what the host sees vs what others see
    (window as { debugComparePlayerCounts?: () => Promise<unknown> }).debugComparePlayerCounts = async () => {
      console.log('[DEBUG] Comparing player counts across all visible rooms...');
      
      try {
        // Get all rooms with individual player records (if RLS allows)
        const { data: roomsWithPlayers, error: roomsError } = await supabase
          .from('game_rooms')
          .select(`
            id,
            title,
            host_id,
            status,
            max_players,
            game_players(
              user_id,
              is_connected
            )
          `)
          .in('status', ['waiting', 'active']);
        
        // Get all rooms with count aggregation
        const { data: roomsWithCounts, error: countsError } = await supabase
          .from('game_rooms')
          .select(`
            id,
            title,
            host_id,
            status,
            max_players,
            game_players(count)
          `)
          .in('status', ['waiting', 'active']);
        
        console.log('[DEBUG] Comparison Results:', {
          roomsWithPlayers: roomsWithPlayers?.map(r => ({
            id: r.id.substring(0, 8) + '...',
            title: r.title,
            hostId: r.host_id.substring(0, 8) + '...',
            isOwnRoom: r.host_id === user?.id,
            playerRecords: r.game_players?.length || 0,
            connectedPlayers: r.game_players?.filter((p: { is_connected: boolean }) => p.is_connected).length || 0
          })),
          roomsWithCounts: roomsWithCounts?.map(r => ({
            id: r.id.substring(0, 8) + '...',
            title: r.title,
            hostId: r.host_id.substring(0, 8) + '...',
            isOwnRoom: r.host_id === user?.id,
            countAggregation: Array.isArray(r.game_players) && r.game_players[0]?.count || 0
          })),
          errors: { roomsError, countsError },
          userContext: user?.id
        });
        
        return {
          roomsWithPlayers,
          roomsWithCounts,
          roomsError,
          countsError,
          comparison: roomsWithPlayers?.map(r => {
            const countRoom = roomsWithCounts?.find(cr => cr.id === r.id);
            return {
              roomId: r.id.substring(0, 8) + '...',
              title: r.title,
              isOwnRoom: r.host_id === user?.id,
              individualRecordsCount: r.game_players?.length || 0,
              countAggregationResult: Array.isArray(countRoom?.game_players) && countRoom.game_players[0]?.count || 0,
              match: (r.game_players?.length || 0) === (Array.isArray(countRoom?.game_players) && countRoom.game_players[0]?.count || 0)
            };
          })
        };
        
      } catch (e) {
        console.error('[DEBUG] Exception comparing player counts:', e);
        return { exception: e };
      }
    };
  }

  // Layer 3: The Reactive UI - Check if current user is marked as disconnected
  const self = playersInRoom.find(p => p.user_id === user?.id);
  const handleRejoinRoom = async () => {
    if (activeRoomId) {
      await handleJoinRoom(activeRoomId);
    }
  };

  // If current user is marked as disconnected, show the DisconnectedOverlay
  // Show loading state while determining authentication status to prevent race condition
  if (isAuthLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center text-white bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-lg">Initializing...</p>
        </div>
      </div>
    );
  }

  if (self && !self.is_connected && selectedOverallGameType === 'multiplayer' && multiplayerPanelState === 'in_room') {
    return <DisconnectedOverlay onReconnect={handleRejoinRoom} />;
  }

  return (
    <main className="flex min-h-screen flex-col items-center p-4 md:p-12 lg:p-24 pt-12 md:pt-20">
      {/* Auth Modal in top-right corner */}
      <div className="absolute top-4 right-4 z-[100]">
        <AuthModal />
      </div>



      {/* Header Row Container for Title and Flanking SP Mode Buttons */}
      <div className="flex items-center justify-center w-full max-w-5xl mb-1 space-x-4 md:space-x-6">
        {/* Timed Mode Button Container (always takes up space) */}
        <div className="w-[150px] md:w-[180px] flex-shrink-0 flex justify-start">
          {selectedOverallGameType === 'single-player' && (
            <Button
              onClick={() => handleModeButtonClick('timed')}
              className={cn(
                "px-5 py-2.5 md:px-7 md:py-3.5 text-lg md:text-xl font-bold rounded-lg",
                "border-yellow-500 border-[3px]",
                "transition-all shadow-md",
                activeGameMode === 'timed' 
                  ? "bg-red-600 text-white ring-4 ring-yellow-500 ring-offset-2 ring-offset-green-950"
                  : "bg-red-700 text-gray-200 hover:bg-red-500"
              )}
            >
              Timed Mode
            </Button>
          )}
        </div>

        {/* Jumbotron Title - Centered */}
        <div
          className="relative bg-[#0A0A0A] p-3 md:p-4 shadow-2xl inline-block border-2 border-[#050505] rounded-[5px]
                     flex-shrink-0
                     [background-image:repeating-radial-gradient(circle_at_center,rgba(255,255,255,0.10)_0,rgba(255,255,255,0.10)_2px,transparent_2px,transparent_100%)]
                     [background-size:8px_8px]
                     [box-shadow:0_0_10px_rgba(0,0,0,0.5),inset_0_0_8px_rgba(80,80,80,0.2),0_2px_10px_1px_rgba(0,0,0,0.35)]
                     after:content-[''] after:absolute after:inset-0
                     after:[background:linear-gradient(rgba(10,10,10,0.2)_50%,transparent_50%)]
                     after:[background-size:100%_6px] after:opacity-50 after:pointer-events-none after:z-[1]
                     before:content-[''] before:absolute before:inset-0 before:rounded-[5px]
                     before:[box-shadow:inset_0_3px_0_0_rgba(255,255,255,0.1),inset_0_-3px_0_0_rgba(0,0,0,0.3),inset_3px_0_0_0_rgba(255,255,255,0.07),inset_-3px_0_0_0_rgba(0,0,0,0.2)]
                     before:pointer-events-none before:z-[2]"
        >
          <h1 className="jumbotron-title text-4xl md:text-5xl uppercase whitespace-nowrap">
            Recognition Combine
          </h1>
        </div>

        {/* Normal Mode Button Container (always takes up space) */}
        <div className="w-[150px] md:w-[180px] flex-shrink-0 flex justify-end">
          {selectedOverallGameType === 'single-player' && (
            <Button
              onClick={() => handleModeButtonClick('normal')}
              className={cn(
                "px-5 py-2.5 md:px-7 md:py-3.5 text-lg md:text-xl font-bold rounded-lg",
                "border-yellow-500 border-[3px]",
                "transition-all shadow-md",
                activeGameMode === 'normal' 
                  ? "bg-blue-600 text-white ring-4 ring-yellow-500 ring-offset-2 ring-offset-green-950"
                  : "bg-blue-700 text-gray-200 hover:bg-blue-600"
              )}
            >
              Normal Mode
            </Button>
          )}
        </div>
      </div>

      {/* Single-player / Multiplayer Mode Selection Buttons */}
      <div 
        className="flex items-center justify-center w-full max-w-lg mb-4 md:mb-6 space-x-0"
        style={{ marginTop: '-5px' }}
      >
        <Button
          onClick={() => handleOverallGameTypeChange('single-player')}
          className={cn(
            "px-5 py-2.5 md:px-7 md:py-3 text-sm md:text-base font-bold rounded-l-lg rounded-r-none",
            "transition-all duration-200 ease-in-out",
            selectedOverallGameType === 'single-player'
              ? "bg-black text-yellow-300 ring-2 ring-lime-500 ring-offset-2 ring-offset-green-900 brightness-95 shadow-inner shadow-lime-500/30 scale-105 z-10"
              : "bg-slate-800 text-slate-400 hover:bg-slate-700 hover:text-lime-400 border border-slate-700 shadow-md"
          )}
        >
          Single-player Mode
        </Button>
        <Button
          onClick={() => handleOverallGameTypeChange('multiplayer')}
          className={cn(
            "px-5 py-2.5 md:px-7 md:py-3 text-sm md:text-base font-bold rounded-r-lg rounded-l-none",
            "transition-all duration-200 ease-in-out",
            selectedOverallGameType === 'multiplayer'
              ? "bg-black text-yellow-300 ring-2 ring-lime-500 ring-offset-2 ring-offset-green-900 brightness-95 shadow-inner shadow-lime-500/30 scale-105 z-10"
              : "bg-slate-800 text-slate-400 hover:bg-slate-700 hover:text-lime-400 border border-slate-700 shadow-md"
          )}
        >
          Multiplayer Mode
        </Button>
      </div>

      {/* Main Content Container */}
      <div className="w-full flex flex-col items-center">
        {/* Main Grid Container - ALWAYS PRESENT with consistent spacing */}
        <div className="w-full max-w-[77rem] mx-auto grid grid-cols-1 md:grid-cols-4 gap-5" style={{ minHeight: '600px' }}>
          {/* Left Panel */}
          <div className="md:col-span-1 bg-green-900 bg-opacity-75 pt-5 px-3 pb-3 rounded-lg border border-green-700 text-white shadow-lg h-[600px] flex flex-col">
            {selectedOverallGameType === 'single-player' ? (
              // Single Player Left Panel Content
              <>
                <div className="flex flex-col items-center">
                  <div className="flex items-baseline justify-center gap-x-3 w-full">
                    <div className="text-center">
                      <h2 className="text-2xl md:text-3xl font-bold mb-0">Score</h2>
                      <span className="text-3xl md:text-4xl font-extrabold text-yellow-400 leading-none block">
                        {score}
                      </span>
                    </div>
                    {activeGameMode === 'timed' && gameStatus === 'playing' && (
                      <div className="text-center">
                        <h2 className="text-lg md:text-xl font-bold mb-0">Timer</h2>
                        <span className="text-2xl md:text-3xl font-extrabold text-orange-400 leading-none block">
                          {formattedTimer}
                        </span>
                      </div>
                    )}
                  </div>
                  <div className="mt-3 text-sm leading-tight text-center w-full">
                    {activeGameMode === 'normal' && (
                      <>
                        <p>Streak: {streak}</p>
                        <p>Best Streak: {bestStreak}</p>
                      </>
                    )}
                    <p>Best Normal: {hasMounted ? bestNormalScore : 0}</p>
                    <p>Best Timed: {hasMounted ? bestTimedScore : 0}</p>
                  </div>
                </div>
                <hr className="my-3 border-green-700" />
                <div className="flex-1 flex flex-col min-h-0 pb-2.5">
                  <h3 className="text-2xl font-semibold mb-1 text-center">Recent Answers</h3>
                  <div className="flex-1 overflow-y-auto pr-2 bg-gray-800 bg-opacity-50">
                    {activeGameMode === 'normal' ? (
                      <RecentAnswersList answers={recentAnswers} onSelect={handleRecentSelect} />
                    ) : (
                      <p className="text-gray-400 text-center mt-4">Recent answers disabled in Timed Mode.</p>
                    )}
                  </div>
                </div>
              </>
            ) : currentRoomGameData && currentRoomGameData.status === 'active' && activeRoomId && multiplayerPanelState === 'in_room' ? (
              // Multiplayer Active Game - Scores Panel
              <div className="flex flex-col h-full">
                <h2 className="text-2xl font-bold mb-2 text-center text-yellow-300 border-b-2 border-yellow-500 pb-2">
                  Player Scores (Round {currentRoomGameData.current_round_number || 0})
                </h2>
                <div className="flex-1 overflow-y-auto space-y-1.5 pr-1 text-sm">
                  {(() => {
                    console.log('[PlayerScoresPanel_ActiveGame] Rendering scores. Players in room:', 
                      playersInRoom.map(p => ({ uid: p.user_id, name: p.profile?.username, connected: p.is_connected })),
                      'Scores from DB:', currentRoomGameData.player_scores
                    );

                    if (playersInRoom.length === 0 && currentRoomGameData.status === 'active') { 
                      return <p className="text-gray-400 italic text-center mt-4">Waiting for players to connect/appear...</p>;
                    }
                    
                    const displayPlayersWithScores = playersInRoom.map(player => {
                      const score = currentRoomGameData.player_scores?.[player.user_id] || 0;
                      const isCurrentUser = player.user_id === user?.id;
                      const isHost = player.user_id === currentRoomGameData.host_id;
                      
                      console.log('[PlayerScoresPanel_ActiveGame] Processing player:', {
                        userId: player.user_id,
                        username: player.profile?.username,
                        score,
                        isCurrentUser,
                        isHost,
                        isConnected: player.is_connected
                      });

                      return {
                        key: player.user_id,
                        userId: player.user_id,
                        username: player.profile?.username || `User...${player.user_id.slice(-4)}`,
                        score,
                        isCurrentUser,
                        isHost,
                        isConnected: player.is_connected
                      };
                    }).sort((a, b) => {
                      // Sort by score (descending), then by username (ascending)
                      if (b.score !== a.score) return b.score - a.score;
                      return a.username.localeCompare(b.username);
                    });

                    console.log('[PlayerScoresPanel_ActiveGame] Displayable players with scores:', displayPlayersWithScores);

                    // If displayPlayersWithScores is STILL empty after mapping (e.g. if playersInRoom had non-game related entries)
                    // This check is mostly defensive if playersInRoom somehow had entries that didn't map to displayable players
                    if (displayPlayersWithScores.length === 0) {
                        return <p className="text-gray-400 italic text-center mt-4">Waiting for players...</p>; // Generic fallback
                    }

                    return displayPlayersWithScores.map((pws, index) => {
                      const rank = index + 1;
                      let trophy = '';
                      if (rank === 1) trophy = '🥇';
                      else if (rank === 2) trophy = '🥈';
                      else if (rank === 3) trophy = '🥉';

                      return (
                        <div
                          key={pws.key}
                          className={cn(
                            "flex justify-between items-center p-2 rounded",
                            "bg-slate-800/70 hover:bg-slate-700/90 transition-colors",
                            !pws.isConnected && "opacity-50 italic",
                            pws.isCurrentUser && "ring-1 ring-yellow-500/50" // Highlight current user
                          )}
                        >
                          <span className="truncate flex items-center">
                            <span className="mr-2 w-5 text-center">{trophy || rank}</span>
                            {pws.username}
                            {pws.isCurrentUser && <span className="text-xs text-blue-400 ml-1">(You)</span>}
                            {pws.isHost && <span className="text-xs text-green-400 ml-1">(Host)</span>}
                            {!pws.isConnected && <span className="text-xs text-red-500 ml-1">(off)</span>}
                          </span>
                          <span className="font-semibold text-yellow-400">{pws.score} pts</span>
                        </div>
                      );
                    });
                  })()}
                </div>
              </div>
            ) : (
              // Multiplayer Lobby - Leaderboards Panel (existing logic)
              <div className="flex flex-col h-full">
                <h2 className="text-2xl font-bold mb-3 text-center text-yellow-300 border-b-2 border-yellow-500 pb-2">Leaderboards</h2>
                <div className="flex-1 overflow-y-auto space-y-3 pr-1 text-sm">
                  {/* Personal Records */}
                  <div className="mb-2">
                    <h3 className="text-lg font-semibold text-lime-300 mb-1">Personal Bests</h3>
                    {personalRecords.length > 0 ? personalRecords.map(pr => (
                      <div key={pr.username} className="flex justify-between text-xs bg-slate-800 px-2 py-1 rounded">
                        <span>{pr.username}</span>
                        <span className="font-semibold">{pr.score}</span>
                      </div>
                    )) : <p className="text-xs text-gray-400 italic">No records yet.</p>}
                  </div>

                  {/* Global Leaderboard */}
                  <div>
                    <button 
                      onClick={() => handleExpandLeaderboard("Global Top 5", globalLeaderboard)}
                      className="text-lg font-semibold text-lime-300 mb-1 w-full text-left hover:text-yellow-300 transition-colors"
                    >
                      Global Top 5 ❯
                    </button>
                    {globalLeaderboard.slice(0, 3).map(entry => (
                      <div key={entry.rank + entry.username} className="flex justify-between text-xs bg-slate-800/50 px-2 py-0.5 rounded mb-0.5">
                        <span>{entry.rank}. {entry.username}</span>
                        <span className="font-semibold">{entry.score}</span>
                      </div>
                    ))}
                  </div>

                  {/* Regional Leaderboards */}
                  {userRegion && (
                    <div>
                      <h3 className="text-lg font-semibold text-lime-300 mt-2 mb-1">Regional (Your: {userRegion})</h3>
                      {regionalLeaderboards.map(rl => (
                        <div key={rl.regionName} className="mb-1">
                          <button 
                            onClick={() => handleExpandLeaderboard(`${rl.regionName} Top 5`, rl.entries)}
                            className="text-sm font-medium text-sky-300 w-full text-left hover:text-sky-200 transition-colors"
                          >
                            {rl.regionName} {rl.regionName === userRegion ? '(Your Region)' : ''} ❯
                          </button>
                          {rl.entries.slice(0,2).map(entry => (
                            <div key={entry.rank + entry.username} className="flex justify-between text-xs bg-slate-800/30 px-2 py-0.5 rounded mb-0.5">
                              <span>{entry.rank}. {entry.username}</span>
                              <span className="font-semibold">{entry.score}</span>
                            </div>
                          ))}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Center Panel */}
          <div className="relative md:col-span-2 bg-green-800 bg-opacity-80 p-4 md:p-6 rounded-lg border-2 border-green-600 text-white flex flex-col items-center justify-center min-h-[600px] h-[600px] shadow-2xl">
            {/* Animation Components */}
            {selectedOverallGameType === 'single-player' && (
              <>
                <ScorePopup scoreChange={lastScoreChange} trigger={animationTrigger} />
                <FootballFx trigger={animationTrigger} streak={streak} />
                <TimeChangePopup
                  timeChange={lastTimeChange}
                  trigger={timeChangeAnimationTrigger}
                  scoreChanged={!!lastScoreChange && lastScoreChange > 0}
                />
              </>
            )}

            {selectedOverallGameType === 'single-player' ? (
              // Single Player Center Panel Content
              <>
                {isLoadingInitialGame ? (
                  <FootballLoader message={loadingMessage} />
                ) : isCountdownActive ? (
                  <div className="text-8xl font-archivo text-yellow-300" style={{textShadow: '3px 3px 5px rgba(0,0,0,0.7)'}}>
                    {countdownValue}
                  </div>
                ) : viewingRecentPlayer ? (
                  <>
                    <PlayerImageDisplay
                      imageUrl={viewingRecentPlayer?.local_image_path ? `/players_images/${viewingRecentPlayer.local_image_path}` : '/images/placeholder.jpg'}
                      altText={viewingRecentPlayer?.player_name || 'Recent Player'}
                    />
                    <h2 className="text-3xl font-bold my-4 text-center">{viewingRecentPlayer?.player_name}</h2>
                    <Button onClick={handleReturnToGame} className="mt-7 bg-sky-600 hover:bg-sky-700 text-white font-bold shadow-lg text-lg">
                      Return to Game
                    </Button>
                  </>
                ) : gameStatus === 'playing' && currentQuestion ? (
                  <>
                    {!isAnswered && (
                      <div className="mb-2 font-archivo font-bold text-white tracking-wider" style={{ fontSize: '1.98rem', textShadow: '2px 2px 4px rgba(0,0,0,0.5)', marginTop: '-35px' }}>
                        WHO&apos;S THIS ACTIVE NFL PLAYER?
                      </div>
                    )}
                    <PlayerImageDisplay
                      imageUrl={currentQuestion?.imageUrl || '/images/placeholder.jpg'}
                      altText={(isAnswered && activeGameMode === 'normal' && currentQuestion?.correctPlayer) ? currentQuestion.correctPlayer.player_name : 'Guess the player'}
                    />
                    <div className="grid grid-cols-2 gap-5 w-full max-w-[440px] mt-4">
                      {currentQuestion?.choices.map((choice, index) => {
                        const choiceKey = `${currentQuestion?.correctPlayer?.id || 'unknown'}-${choice.name}-${index}`;
                        const wasChosen = isAnswered && userChoiceName === choice.name;

                        return (
                          <ChoiceButton
                            key={choiceKey}
                            choiceText={choice.name}
                            onClick={() => submitAnswer(choice.name)}
                            disabled={isAnswered}
                            isCorrect={(isAnswered && activeGameMode === 'normal') ? choice.isCorrect : null}
                            wasChosen={wasChosen}
                          />
                        );
                      })}
                    </div>
                    {isAnswered && activeGameMode === 'normal' && (
                      <Button onClick={handleNextQuestionClick} className="mt-7 bg-yellow-500 hover:bg-yellow-600 text-black font-bold shadow-lg text-lg">
                        Next Question
                      </Button>
                    )}
                  </>
                ) : gameStatus === 'finished' && (
                  <div className="flex flex-col items-center justify-center h-full">
                    <h2 className="text-4xl font-bold mb-2 text-yellow-300">Game Over!</h2>
                    <p className="text-2xl mb-4">Your {activeGameMode === 'timed' ? 'Timed Mode' : ''} Score: {score}</p>
                    {activeGameMode === 'timed' && <p className="text-lg mb-4">Best Timed: {bestTimedScore}</p>}
                    {activeGameMode === 'normal' && <p className="text-lg mb-4">Best Normal: {bestNormalScore}</p>}
                    <Button onClick={handleGameResetClick} className="bg-lime-600 hover:bg-lime-700 text-black font-bold shadow-lg text-lg px-6 py-3">
                      Play {activeGameMode === 'timed' ? 'Timed Mode' : 'Normal Mode'} Again
                    </Button>
                  </div>
                )}
              </>
            ) : centerPanelMpState === 'expanded_leaderboard' && expandedLeaderboardData ? (
              // Expanded Leaderboard View
              <div className="w-full h-full flex flex-col p-2">
                <div className="flex justify-between items-center mb-3">
                  <h2 className="text-3xl font-bold text-yellow-300">{expandedLeaderboardData?.title || 'Leaderboard'}</h2>
                  <Button 
                    onClick={() => { setExpandedLeaderboardData(null); setCenterPanelMpState('lobby_list_detail');}} 
                    className="bg-slate-700 hover:bg-slate-600 text-xs py-1 px-2"
                  >
                    Back to Lobby
                  </Button>
                </div>
                <div className="flex-1 overflow-y-auto bg-slate-900/50 p-3 rounded-md">
                  {expandedLeaderboardData?.entries.map(entry => (
                    <div key={entry.rank + entry.username} className="flex justify-between items-center p-2 border-b border-slate-700 hover:bg-slate-700/50 rounded">
                      <span className="text-lg">{entry.rank}. {entry.username}</span>
                      <span className="text-xl font-semibold text-yellow-400">{entry.score}</span>
                    </div>
                  ))}
                </div>
              </div>
            ) : centerPanelMpState === 'lobby_list_detail' ? (
              <div className="w-full h-full flex flex-col items-center justify-start p-2">
                <div className="flex justify-between items-center mb-2 w-full">
                  <h2 className="text-3xl font-bold text-yellow-300">
                    {selectedRoomForDetail ? `Room: ${selectedRoomForDetail.title || ('...' + selectedRoomForDetail.id.slice(-6))}` : "Game Lobby"}
                  </h2>
                  {!selectedRoomForDetail && (
                    <div className="flex gap-2">
                      <Button
                        onClick={async () => {
                          console.log("[Client] Refresh List button clicked.");
                          await fetchAndSetGameRooms();
                        }}
                        disabled={isLoadingRooms}
                        className="px-3 py-1 text-xs bg-sky-600 hover:bg-sky-700 text-white rounded-md shadow-md"
                      >
                        {isLoadingRooms ? 'Refreshing...' : 'Refresh List'}
                      </Button>
                    </div>
                  )}
                </div>
                <ConnectionStatusIndicator status={connectionStatus} />
                {errorMp && !selectedRoomForDetail && (
                  <p className="text-red-400 bg-red-900 p-2 rounded text-xs mb-3 text-center w-full">{errorMp}</p>
                )}
                
                {lobbyFetchError && !selectedRoomForDetail && !isLoadingRooms && (
                  <div className="text-center text-red-400 bg-red-900/50 p-3 rounded text-xs mb-3 w-full">
                    <p>Could not load games. Please try again.</p>
                    <Button
                      onClick={async () => {
                        console.log("[Client] Retry fetch button clicked after error.");
                        await fetchAndSetGameRooms();
                      }}
                      className="mt-2 px-3 py-1 text-xs bg-red-600 hover:bg-red-700 text-white rounded-md"
                    >
                      Retry
                    </Button>
                  </div>
                )}

                {selectedRoomForDetail ? (
                  <div className="w-full bg-slate-800/60 p-4 rounded-lg shadow-lg text-sm">
                    <p><span className="font-semibold text-lime-300">Mode:</span> <span className="capitalize">{selectedRoomForDetail.multiplayer_mode?.replace('_',' ')}</span></p>
                    <p><span className="font-semibold text-lime-300">Host:</span> {selectedRoomForDetail.profiles?.username || 'Unknown Host'}</p>
                    <p><span className="font-semibold text-lime-300">Status:</span> <span className="capitalize">{selectedRoomForDetail.status}</span></p>
                    <p>
                      <span className="font-semibold text-lime-300">Players:</span> {selectedRoomForDetail.connected_players ?? 0} / {selectedRoomForDetail.max_players ?? 8}
                    </p>
                    <p className="mt-1"><span className="font-semibold text-lime-300">Code:</span> {selectedRoomForDetail.room_code || 'N/A'}</p>
                    
                    {user ? (() => {
                      // ENHANCED LOGIC: More precise determination of user's relationship to this room
                      console.log('[LobbyDetail] ENHANCED room relationship analysis:', {
                        roomId: selectedRoomForDetail.id,
                        roomStatus: selectedRoomForDetail.status,
                        userId: user.id,
                        roomHostId: selectedRoomForDetail.host_id,
                        isCurrentUserHost: selectedRoomForDetail.host_id === user.id,
                        currentActiveRoomId: activeRoomId,
                        isActiveRoomMatchingDetailRoom: activeRoomId === selectedRoomForDetail.id,
                        playersInDetailRoomGamePlayers: selectedRoomForDetail.game_players?.length || 0,
                        userInDetailRoomGamePlayers: selectedRoomForDetail.game_players?.some(gp => gp.user_id === user.id) || false,
                        currentPlayersInRoomCount: playersInRoom.length,
                        userInCurrentPlayersInRoom: playersInRoom.some(p => p.user_id === user.id),
                        clientSideConnectionConsistency: {
                          activeRoomIdSet: !!activeRoomId,
                          playersInRoomHasUser: playersInRoom.some(p => p.user_id === user.id),
                          activeRoomMatchesDetailRoom: activeRoomId === selectedRoomForDetail.id,
                          consistencyCheck: !activeRoomId || (activeRoomId === selectedRoomForDetail.id && playersInRoom.some(p => p.user_id === user.id))
                        },
                        timestamp: new Date().toISOString()
                      });

                      // Find if the current logged-in user has an entry in the game_players list of the room being detailed
                      const playerEntryInDetailRoom = selectedRoomForDetail.game_players?.find(
                        (gp) => gp.user_id === user.id
                      );

                      // CRITICAL FIX: Check if user was an original player (for reconnection after host migration)
                      const wasOriginalPlayer = selectedRoomForDetail.original_player_ids?.includes(user.id) || false;
                      
                      // CRITICAL FIX: Determine if user is "actively connected" to this specific room
                      // This should only be true if:
                      // 1. activeRoomId matches this room's ID AND
                      // 2. playersInRoom includes the current user AND
                      // 3. The user actually joined/created this room in the current session
                      const isActivelyConnectedToThisRoom = 
                        activeRoomId === selectedRoomForDetail.id && 
                        playersInRoom.some(p => p.user_id === user.id) &&
                        multiplayerPanelState === 'in_room'; // Additional check: user should be in "in_room" state
                      
                      console.log('[LobbyDetail] Connection status determination:', {
                        playerEntryInDetailRoom: !!playerEntryInDetailRoom,
                        playerEntryConnected: playerEntryInDetailRoom?.is_connected,
                        wasOriginalPlayer,
                        originalPlayerIds: selectedRoomForDetail.original_player_ids,
                        isActivelyConnectedToThisRoom,
                        rationale: {
                          activeRoomIdMatches: activeRoomId === selectedRoomForDetail.id,
                          userInPlayersInRoom: playersInRoom.some(p => p.user_id === user.id),
                          inRoomState: multiplayerPanelState === 'in_room',
                          allConditionsMet: isActivelyConnectedToThisRoom
                        }
                      });

                      if (selectedRoomForDetail.status === 'waiting') {
                        // Room is WAITING
                        if (isActivelyConnectedToThisRoom) {
                          // User is ACTIVELY connected to this waiting room (should not normally show detail view)
                          console.log('[LobbyDetail] User is actively connected to this waiting room - showing status message');
                          return <p className="text-center text-gray-400 mt-3">You are currently in this waiting room.</p>;
                        } else if (playerEntryInDetailRoom && !playerEntryInDetailRoom.is_connected) {
                          // User has an entry but is disconnected -> "Rejoin Waiting Room"
                          return (
                            <Button
                              onClick={() => handleJoinRoom(selectedRoomForDetail.id)}
                              disabled={isJoiningOrRejoiningRoom}
                              className="w-full mt-3 bg-orange-500 hover:bg-orange-600 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isJoiningOrRejoiningRoom ? "Rejoining..." : "Rejoin Waiting Room"}
                            </Button>
                          );
                        } else if (!playerEntryInDetailRoom) {
                          // User has no entry in this waiting room -> "Join This Room"
                          const isFull = selectedRoomForDetail.connected_players != null &&
                                        selectedRoomForDetail.max_players != null &&
                                        selectedRoomForDetail.connected_players >= selectedRoomForDetail.max_players;
                          return (
                            <Button
                              onClick={() => handleJoinRoom(selectedRoomForDetail.id)}
                              className="w-full mt-3 bg-blue-600 hover:bg-blue-700 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed"
                              disabled={isFull || isJoiningOrRejoiningRoom}
                            >
                              {isJoiningOrRejoiningRoom ? "Joining..." : isFull ? "Room Full" : "Join This Room"}
                            </Button>
                          );
                        } else {
                          // playerEntryInDetailRoom exists and is connected, but user is not actively connected client-side
                          // This might be a data inconsistency - show rejoin option
                          console.log('[LobbyDetail] Data inconsistency detected: user has connected DB entry but no active client connection');
                          return (
                            <Button
                              onClick={() => handleJoinRoom(selectedRoomForDetail.id)}
                              disabled={isJoiningOrRejoiningRoom}
                              className="w-full mt-3 bg-orange-500 hover:bg-orange-600 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isJoiningOrRejoiningRoom ? "Reconnecting..." : "Reconnect to Room"}
                            </Button>
                          );
                        }
                      } else if (selectedRoomForDetail.status === 'active') {
                        // Room is ACTIVE
                        const isCurrentUserHost = selectedRoomForDetail.host_id === user.id;
                        
                        if (isActivelyConnectedToThisRoom) {
                          // Client is actively connected to this active room
                          console.log('[LobbyDetail] Client is actively connected to this active room');
                          return <p className="text-center text-gray-400 mt-3">You are currently in this active game.</p>;
                        } else if (playerEntryInDetailRoom && !playerEntryInDetailRoom.is_connected) {
                          // User was part of this active game and is disconnected -> "Rejoin Active Game"
                          return (
                            <Button
                              onClick={() => handleJoinRoom(selectedRoomForDetail.id)}
                              disabled={isJoiningOrRejoiningRoom}
                              className="w-full mt-3 bg-green-600 hover:bg-green-700 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isJoiningOrRejoiningRoom ? "Rejoining..." : "Rejoin Active Game"}
                            </Button>
                          );
                        } else if (playerEntryInDetailRoom && playerEntryInDetailRoom.is_connected && !isActivelyConnectedToThisRoom) {
                          // User is part of this active game, DB shows connected, BUT client is NOT actively connected
                          // This happens after logout/login - need to re-establish client connection
                          const buttonText = isCurrentUserHost ? "Re-enter Your Game" : "Rejoin Active Game";
                          const buttonColor = isCurrentUserHost ? "bg-blue-600 hover:bg-blue-700" : "bg-green-600 hover:bg-green-700";
                          
                          return (
                            <Button
                              onClick={() => handleJoinRoom(selectedRoomForDetail.id)}
                              disabled={isJoiningOrRejoiningRoom}
                              className={`w-full mt-3 ${buttonColor} py-1.5 disabled:opacity-50 disabled:cursor-not-allowed`}
                            >
                              {isJoiningOrRejoiningRoom ? (isCurrentUserHost ? "Re-entering..." : "Rejoining...") : buttonText}
                            </Button>
                          );
                        } else if (!playerEntryInDetailRoom && isCurrentUserHost) {
                          // Host is viewing their own active game but has NO player entry
                          // This happens when host used "Leave Game" and leave-room-handler deleted their record
                          console.log('[LobbyDetail] Host is viewing their active game with no player entry. Offering RE-ENTER option.');
                          return (
                            <Button
                              onClick={() => handleJoinRoom(selectedRoomForDetail.id)}
                              disabled={isJoiningOrRejoiningRoom}
                              className="w-full mt-3 bg-blue-600 hover:bg-blue-700 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isJoiningOrRejoiningRoom ? "Re-entering..." : "Re-enter Your Game"}
                            </Button>
                          );
                        } else if (!playerEntryInDetailRoom && wasOriginalPlayer) {
                          // CRITICAL FIX: User was an original player but is no longer in game_players (after host migration)
                          // This happens when the original host left and was removed from game_players
                          console.log('[LobbyDetail] Original player viewing active game with no current player entry. Offering REJOIN option.');
                          return (
                            <Button
                              onClick={() => handleJoinRoom(selectedRoomForDetail.id)}
                              disabled={isJoiningOrRejoiningRoom}
                              className="w-full mt-3 bg-green-600 hover:bg-green-700 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {isJoiningOrRejoiningRoom ? "Rejoining..." : "Rejoin Active Game"}
                            </Button>
                          );
                        } else {
                          // Game is active, and user was NOT part of it AND is not the host AND was not an original player
                          console.log('[LobbyDetail] Non-participant viewing an active game with no player entry');
                          return <p className="text-center text-yellow-400 mt-3">Game in progress. Cannot join.</p>;
                        }
                      } else if (selectedRoomForDetail.status === 'finished') {
                        // Room is FINISHED
                        return <p className="text-center text-gray-400 mt-3">This game has finished.</p>;
                      }

                      return null; // Default: no button if other conditions aren't met
                    })() : (
                      <p className="text-center text-gray-400 mt-3">Please sign in to join rooms.</p>
                    )}
                    
                    {/* "Back to List" Button - always shown when selectedRoomForDetail is active */}
                    <Button 
                      onClick={() => {
                        console.log('[BackToList] Navigating back to lobby list from room detail view');
                        setSelectedRoomForDetail(null);
                        setMultiplayerPanelState('lobby_list');
                        setCenterPanelMpState('lobby_list_detail');
                      }} 
                      className="w-full mt-2 bg-gray-600 hover:bg-gray-500 text-xs py-1"
                    >
                      Back to List
                    </Button>
                  </div>
                ) : (
                  <div className="w-full flex-1 overflow-y-auto space-y-2 pr-1">
                    {(() => {
                      console.log("[RoomList Render Attempt] isLoadingRooms:", isLoadingRooms, "gameRooms:", gameRooms, "gameRooms.length:", gameRooms?.length);
                      return null;
                    })()}

                    {isLoadingRooms && (
                      <p className="text-center text-gray-300 pt-5">Loading available games...</p>
                    )}
                    
                    {!isLoadingRooms && gameRooms && gameRooms.length === 0 && (
                      <p className="text-center text-gray-300 pt-5">No public games available. Create one!</p>
                    )}

                    {!isLoadingRooms && gameRooms && gameRooms.length > 0 && (
                      gameRooms.map(room => (
                        <div
                          key={room.id}
                          className="bg-slate-800 p-3 rounded-lg shadow-lg border-2 border-transparent hover:border-yellow-500 cursor-pointer transition-all"
                          onClick={() => handleViewLobbyDetail(room)}
                          onDoubleClick={() => user ? handleJoinRoom(room.id) : null}
                        >
                          <div className="flex justify-between items-center mb-1">
                            <h4 className="text-md font-bold text-yellow-400 truncate">
                              {room.title || `Room ${room.id.slice(-6)}`}
                            </h4>
                            <span className="text-xs bg-sky-600 px-1.5 py-0.5 rounded capitalize">
                              {room.multiplayer_mode === 'competitive' ? (
                                <span className="text-xs text-yellow-400">Competitive</span>
                              ) : (
                                <span className="text-xs text-blue-400">Cooperative</span>
                              )}
                            </span>
                          </div>
                          <p className="text-xs text-gray-300 truncate">
                            Host: {room.profiles?.username || 'Unknown'} • Players: {room.connected_players ?? 0}/{room.max_players ?? 8}
                          </p>
                        </div>
                      ))
                    )}
                  </div>
                )}
              </div>
            ) : centerPanelMpState === 'mp_game_active' && activeRoomId ? (
              // Active Multiplayer Game
              <div>
                {(() => {
                  // First, ensure all necessary data exists and has the correct type
                  if (
                    currentRoomGameData &&
                    currentRoomGameData.status === 'active' &&
                    currentRoomGameData.current_question_data &&
                    typeof currentRoomGameData.current_question_data.questionId === 'string' &&
                    Array.isArray(currentRoomGameData.current_question_data.choices)
                  ) {
                    // If all checks pass, we can safely access the properties
                    const questionData = currentRoomGameData.current_question_data;

                    return (
                      <div className="flex flex-col items-center">
                        <div className="mb-2 font-archivo font-bold text-white tracking-wider" style={{ fontSize: '1.98rem', textShadow: '2px 2px 4px rgba(0,0,0,0.5)', marginTop: '-35px' }}>
                          WHO&apos;S THIS ACTIVE NFL PLAYER?
                        </div>
                        <PlayerImageDisplay
                          imageUrl={questionData.imageUrl || '/images/placeholder.jpg'}
                          altText="Guess the player"
                        />
                        <div className="grid grid-cols-2 gap-5 w-full max-w-[440px] mt-4">
                          {questionData.choices.map((choice: PlayerChoice, index: number) => (
                            <ChoiceButton
                              key={`${questionData.questionId}-${index}`}
                              choiceText={choice.name}
                              onClick={() => handleMultiplayerAnswerSubmit(choice.name)}
                              disabled={isSubmittingAnswer || hasSubmittedCurrentRound}
                            />
                          ))}
                        </div>

                        {/* Answer submission status */}
                        <div className="mt-3 text-center">
                          {isSubmittingAnswer && (
                            <p className="text-yellow-400 text-sm font-medium">
                              Submitting answer...
                            </p>
                          )}
                          {hasSubmittedCurrentRound && !isSubmittingAnswer && (
                            <p className="text-green-400 text-sm font-medium">
                              ✓ Answer submitted! Waiting for other players...
                            </p>
                          )}
                        </div>


                      </div>
                    );
                  } else if (currentRoomGameData?.status === 'waiting') {
                    return (
                      <div className="text-center">
                        <h2 className="text-3xl font-bold text-yellow-300">Waiting for game to start...</h2>
                        <p className="mt-2">Room: {currentRoomGameData.title || `...${activeRoomId?.slice(-6)}`}</p>
                      </div>
                    );
                  } else {
                    return (
                      <div className="text-center">
                        <h2 className="text-4xl font-bold text-yellow-300">Game Lobby Active</h2>
                        <p className="text-xl mt-4">Waiting for game data or game has ended.</p>
                      </div>
                    );
                  }
                })()}
              </div>
            ) : (
              <div className="text-gray-400">Error: Invalid multiplayer state.</div>
            )}
          </div>

          {/* Right Panel */}
          <div className="md:col-span-1 bg-green-900 bg-opacity-75 pt-5 px-3 pb-3 rounded-lg border border-green-700 text-white shadow-lg h-[600px] flex flex-col">
            {selectedOverallGameType === 'single-player' ? (
              <PlayerInfoPanel player={playerToShow} />
            ) : multiplayerPanelState === 'lobby_list' ? (
              // Lobby Actions / Create Room
              <div className="flex flex-col h-full">
                <h2 className="text-2xl font-bold mb-3 text-center text-yellow-300 border-b-2 border-yellow-500 pb-2">Lobby Actions</h2>
                <ConnectionStatusIndicator status={connectionStatus} />
                {errorMp && centerPanelMpState === 'lobby_list_detail' && !selectedRoomForDetail && (
                  <p className="text-red-400 bg-red-900 p-2 rounded text-xs mb-2 text-center">{errorMp}</p>
                )}

                <div className="p-3 bg-slate-800/50 rounded-md border border-slate-700 mb-4">
                  <h3 className="text-lg font-semibold mb-2 text-center text-lime-300">Create New Game</h3>
                  
                  {/* Game Mode Selection */}
                  <div className="mb-3 text-sm">
                    <p className="font-medium mb-1 text-gray-300">Game Mode:</p>
                    <div className="flex justify-around space-x-2">
                      <div>
                        <input 
                          type="radio" 
                          id="competitive_lobby" 
                          name="mpModeLobby" 
                          value="competitive" 
                          checked={newRoomMode === 'competitive'} 
                          onChange={() => setNewRoomMode('competitive')} 
                          className="mr-1 accent-yellow-400"
                        />
                        <label htmlFor="competitive_lobby" className="text-gray-200 text-xs cursor-pointer">Competitive</label>
                      </div>
                      <div>
                        <input 
                          type="radio" 
                          id="cooperative_lobby" 
                          name="mpModeLobby" 
                          value="cooperative" 
                          checked={newRoomMode === 'cooperative'} 
                          onChange={() => setNewRoomMode('cooperative')} 
                          className="mr-1 accent-yellow-400"
                        />
                        <label htmlFor="cooperative_lobby" className="text-gray-200 text-xs cursor-pointer">Cooperative</label>
                      </div>
                    </div>
                  </div>

                  {/* Game Duration Selection */}
                  <div className="mb-3 text-sm">
                    <p className="font-medium mb-1 text-gray-300">Game Duration:</p>
                    <div className="grid grid-cols-2 gap-2">
                      {[30, 60, 90, 120].map((duration) => (
                        <div key={duration} className="flex items-center">
                          <input
                            type="radio"
                            id={`duration_${duration}`}
                            name="gameDuration"
                            value={duration}
                            checked={selectedGameDuration === duration}
                            onChange={() => setSelectedGameDuration(duration as GameDuration)}
                            className="mr-1 accent-yellow-400"
                          />
                          <label htmlFor={`duration_${duration}`} className="text-gray-200 text-xs cursor-pointer">
                            {duration} seconds
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Max Players Selection */}
                  <div className="mb-3 text-sm">
                    <p className="font-medium mb-1 text-gray-300">Max Players:</p>
                    <div className="grid grid-cols-2 gap-2">
                      {[2, 4, 6, 8].map((max) => (
                        <div key={max} className="flex items-center">
                          <input
                            type="radio"
                            id={`max_players_${max}`}
                            name="maxPlayers"
                            value={max}
                            checked={selectedMaxPlayers === max}
                            onChange={() => setSelectedMaxPlayers(max as MaxPlayers)}
                            className="mr-1 accent-yellow-400"
                          />
                          <label htmlFor={`max_players_${max}`} className="text-gray-200 text-xs cursor-pointer">
                            {max} Players
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Room Title (Optional) */}
                  <div className="mb-3 text-sm">
                    <label htmlFor="roomTitle" className="block text-xs font-medium text-gray-300 mb-0.5">Room Name (optional):</label>
                    <p className="text-xs text-gray-400 mt-0.5">Default: {(userProfile?.username || "Your") + "'s Game"}</p>
                  </div>

                  {/* Create Room Button */}
                  <Button
                    onClick={handleCreateRoom}
                    disabled={!user || isLoadingRooms || isCreatingRoom || !isOnline}
                    title={!isOnline ? "You are offline. Please check your connection." : "Create a new game"}
                    className={cn(
                      "w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-1.5 text-sm rounded shadow-md",
                      "transition-all duration-200",
                      isCreatingRoom && "opacity-75 cursor-not-allowed"
                    )}
                  >
                    {(() => {
                      // Debug: Log the Host Game button state
                      const isDisabled = !user || isLoadingRooms || isCreatingRoom || !isOnline;
                      console.log('[HOST_GAME_BUTTON_DEBUG] Button state check:', {
                        isDisabled,
                        hasUser: !!user,
                        userId: user?.id,
                        isLoadingRooms,
                        isCreatingRoom,
                        isOnline,
                        userObject: user ? { id: user.id, email: user.email } : null,
                        timestamp: new Date().toISOString()
                      });
                      return isCreatingRoom ? "Creating..." : isLoadingRooms ? "..." : "Host Game";
                    })()}
                  </Button>
                </div>

                <p className="text-xs text-center text-gray-400 mb-3">
                  View available games in the center panel. Click to see details, double-click to join.
                </p>
                <div className="mt-auto border-t border-slate-700 pt-2">
                  <p className="text-xs text-gray-500 text-center">Tip: Click a room in the center to see details, then join.</p>
                </div>
              </div>
            ) : multiplayerPanelState === 'in_room' && activeRoomId ? (
              // In-Room Info / Player List OR Round Submissions
              <div className="flex flex-col h-full">
                {/* Conditional rendering based on game status */}
                {currentRoomGameData?.status === 'waiting' ? (
                  <>
                    {/* ----- WAITING STATE UI ----- */}
                    <h2 className="text-2xl font-bold mb-3 text-center text-yellow-300 border-b-2 border-yellow-500 pb-2">
                      Room: {currentRoomGameData.title || `...${activeRoomId.slice(-6)}`}
                    </h2>
                    <ConnectionStatusIndicator status={connectionStatus} />
                    {errorMp && (
                      <p className="text-red-400 bg-red-900 p-2 rounded text-xs mb-2 text-center">{errorMp}</p>
                    )}
                    <div className="flex-1 bg-slate-800/50 p-2 rounded mb-2 overflow-y-auto">
                      <div className="flex justify-between items-center mb-2">
                        <p className="text-sm text-lime-300">
                          {(() => {
                            // **CRITICAL: Log exactly what data is being used for the player count display**
                            const displayText = `Players (${playersInRoom.length}/${currentRoomGameData.max_players || 8})`;
                            console.log('[RENDER HOST] [PLAYER_COUNT_DISPLAY] Rendering player count display:', {
                              displayText,
                              playersInRoomLength: playersInRoom.length,
                              maxPlayers: currentRoomGameData.max_players,
                              playersInRoomActualData: JSON.parse(JSON.stringify(playersInRoom)),
                              timestamp: new Date().toISOString()
                            });
                            return displayText;
                          })()}:
                        </p>
                        {/* Show "Ready Up" button only if game is waiting */}
                        {user && playersInRoom.some(p => p.user_id === user.id) && (
                          <Button
                            onClick={handleToggleReady}
                            disabled={isSubmittingReady || connectionStatus === 'OFFLINE' || !isOnline}
                            title={
                              !isOnline
                                ? "You are offline. Please check your connection."
                                : connectionStatus === 'RECONNECTING'
                                  ? "Connection is recovering. You can still ready up."
                                  : connectionStatus === 'OFFLINE'
                                    ? "Connection lost. Please refresh the page."
                                    : ""
                            }
                            className={cn(
                              "text-xs px-2 py-1 transition-colors",
                              (isSubmittingReady || connectionStatus === 'OFFLINE' || !isOnline)
                                ? "bg-gray-400 cursor-not-allowed"
                                : playersInRoom.find(p => p.user_id === user.id)?.is_ready
                                  ? "bg-green-600 hover:bg-green-700"
                                  : "bg-yellow-600 hover:bg-yellow-700"
                            )}
                          >
                            {isSubmittingReady
                              ? "Processing..."
                              : connectionStatus === 'RECONNECTING'
                                ? "Reconnecting..."
                                : playersInRoom.find(p => p.user_id === user.id)?.is_ready
                                  ? "Ready ✓"
                                  : "Ready Up"}
                          </Button>
                        )}
                      </div>
                      {isLoadingPlayers ? (
                        <p className="text-xs text-gray-400 italic">Loading players...</p>
                      ) : playersInRoom.length === 0 ? (
                        <p className="text-xs text-gray-400 italic">Waiting for players...</p>
                      ) : (
                        (() => {
                          // **CRITICAL: Log exactly what players are being rendered in the list**
                          console.log('[RENDER HOST] [PLAYER_LIST_RENDER] About to render player list:', {
                            playersCount: playersInRoom.length,
                            playersToRender: playersInRoom.map(p => ({
                              userId: p.user_id,
                              username: p.profile?.username,
                              isReady: p.is_ready,
                              isConnected: p.is_connected
                            })),
                            timestamp: new Date().toISOString()
                          });
                          return playersInRoom.map(player => (
                            <div
                              key={player.user_id}
                              className={cn(
                                "text-xs p-1.5 rounded mb-1 flex justify-between items-center",
                                "bg-slate-700/50 hover:bg-slate-700/70 transition-colors",
                                player.user_id === user?.id && "bg-slate-600/70", // Highlight current user
                                // Layer 3: The Reactive UI - functionally apparent disconnection
                                !player.is_connected && "opacity-50 grayscale"
                              )}
                              title={!player.is_connected ? `${player.profile?.username || 'Player'} is disconnected` : ''}
                            >
                              <span className="truncate">
                                {player.profile?.username || `Player...${player.user_id.slice(-4)}`}
                                {player.user_id === user?.id && " (You)"}
                                {currentRoomGameData?.host_id === player.user_id && " (Host)"}
                                {!player.is_connected && <span className="ml-1 text-red-400">🔌</span>}
                              </span>
                              {player.is_ready === true && (
                                <span className="text-green-400 ml-2">✓</span>
                              )}
                            </div>
                          ));
                        })()
                      )}
                    </div>

                    {/* Show "Start Game" button only if user is host and game is waiting */}
                    {user && currentRoomGameData?.host_id === user.id && currentRoomGameData?.status === 'waiting' && (
                      <Button
                        onClick={handleStartGame}
                        disabled={
                          isStartingGame ||
                          !playersInRoom.every(p => p.is_ready) ||
                          playersInRoom.length < 2 || // Assuming min 2 players to start
                          connectionStatus === 'OFFLINE' || // Only disable if completely offline, not during reconnecting
                          !isOnline
                        }
                        title={
                          !isOnline
                            ? "You are offline. Please check your connection."
                            : connectionStatus === 'RECONNECTING'
                              ? "Connection is recovering. You can still start the game."
                              : connectionStatus === 'OFFLINE'
                                ? "Connection lost. Please refresh the page."
                                : ""
                        }
                        className={cn(
                          "w-full mt-2 mb-2 py-2 text-lg font-bold",
                          "bg-green-600 hover:bg-green-700 text-white",
                          (isStartingGame || !playersInRoom.every(p => p.is_ready) || playersInRoom.length < 2 || connectionStatus === 'OFFLINE' || !isOnline) && "opacity-50 cursor-not-allowed"
                        )}
                      >
                        {(() => {
                          // **CRITICAL: Log exactly what text is being rendered on the Start Game button**
                          const playerCount = playersInRoom.length;
                          const needMorePlayers = playerCount < 2;
                          const playersNeeded = needMorePlayers ? 2 - playerCount : 0;
                          const allReady = playersInRoom.every(p => p.is_ready);
                          
                          const buttonText = isStartingGame
                            ? "Starting Game..."
                            : needMorePlayers
                              ? `Need ${playersNeeded} more player(s)`
                              : !allReady
                                ? "Waiting for all to ready..."
                                : "Start Game";
                          
                          console.log('[RENDER HOST] [START_BUTTON_TEXT] Rendering Start Game button text:', {
                            buttonText,
                            playerCount,
                            needMorePlayers,
                            playersNeeded,
                            allReady,
                            isStartingGame,
                            playersInRoomData: JSON.parse(JSON.stringify(playersInRoom)),
                            timestamp: new Date().toISOString()
                          });
                          
                          return buttonText;
                        })()}
                      </Button>
                    )}
                    <div className="mt-auto border-t border-slate-700 pt-2">
                      <p className="text-xs text-gray-500 text-center">
                        {(() => {
                          // **CRITICAL: Log exactly what data is being used for the bottom status message**
                          const isHost = user && currentRoomGameData?.host_id === user.id;
                          const playerCount = playersInRoom.length;
                          const needMorePlayers = playerCount < 2;
                          const playersNeeded = needMorePlayers ? 2 - playerCount : 0;
                          const allReady = playersInRoom.every(p => p.is_ready);

                          const statusText = isHost
                            ? (needMorePlayers ? `Need ${playersNeeded} more players to start.` : !allReady ? "Waiting for players to ready up." : "Ready to start game!")
                            : "Waiting for host to start...";

                          console.log('[RENDER HOST] [BOTTOM_STATUS_TEXT] Rendering bottom status text:', {
                            statusText,
                            isHost,
                            playerCount,
                            needMorePlayers,
                            playersNeeded,
                            allReady,
                            playersInRoomData: JSON.parse(JSON.stringify(playersInRoom)),
                            timestamp: new Date().toISOString()
                          });

                          return statusText;
                        })()}
                      </p>
                      <Button
                        onClick={handleLeaveRoom}
                        className="w-full mt-3 bg-red-600 hover:bg-red-700"
                      >
                        Leave Game
                      </Button>
                    </div>
                  </>
                ) : currentRoomGameData?.status === 'active' ? (
                  <>
                    {/* ----- ACTIVE GAME STATE UI (Round Submissions Only) ----- */}
                    <h2 className="text-2xl font-bold mb-3 text-center text-yellow-300 border-b-2 border-yellow-500 pb-2">
                      Round Submissions
                    </h2>
                    <div className="flex-1 space-y-1.5 max-h-[calc(100%-60px)] overflow-y-auto pr-1">
                      {allAnswers.length === 0 ? (
                        <p className="text-gray-400 italic text-center mt-4">No submissions yet...</p>
                      ) : (
                        allAnswers.map((answer) => {
                          // Define a stable key and check if this answer should be animating
                          const answerKey = `${answer.userId}-${answer.questionId}`;
                          const isAnimating = animatingAnswers.has(answerKey);

                          const player = playersInRoom.find(p => p.user_id === answer.userId);

                          // Improved player name lookup with fallbacks
                          let playerName = player?.profile?.username;
                          if (!playerName && answer.userId === user?.id && userProfile?.username) {
                            // Current user fallback - use their profile username
                            playerName = userProfile.username;
                          }
                          if (!playerName) {
                            playerName = `User...${answer.userId.slice(-4)}`;
                          }

                          // Get enhanced animation data for the football effect
                          const enhancedAnimationData = enhancedAnimatedAnswers[answer.userId];

                          console.log('[CARD_COLOR_DEBUG] Rendering username card for answer:', {
                            userId: answer.userId.substring(0, 8) + '...',
                            isCurrentUser: answer.userId === user?.id,
                            choiceName: answer.choiceName,
                            isCorrect: answer.isCorrect,
                            isOptimistic: answer.isOptimistic,
                            willShowGreen: answer.isOptimistic ? answer.isCorrect : answer.isCorrect,
                            willShowRed: answer.isOptimistic ? !answer.isCorrect : !answer.isCorrect
                          });

                          return (
                            <div
                              // MODIFIED: Use the stable key
                              key={answerKey}
                              className={cn(
                                "text-xs p-1.5 rounded relative flex justify-center items-center",
                                // MODIFIED: Animation class is now conditional
                                isAnimating && "animate-slide-up",
                                answer.isOptimistic
                                  ? answer.isCorrect
                                    ? "bg-green-600/50 border border-green-400/70 shadow-[0_0_8px_1px_rgba(77,255,77,0.3)] animate-pulse"
                                    : "bg-red-600/50 border border-red-400/70 animate-pulse"
                                  : answer.isCorrect
                                    ? "bg-green-600/70 border border-green-400 shadow-[0_0_8px_1px_rgba(77,255,77,0.5)]"
                                    : "bg-red-600/70 border border-red-400"
                              )}
                              // MODIFIED: This now ONLY cleans up the slide-in animation.
                              onAnimationEnd={(e) => {
                                if (e.animationName === 'slide-up') {
                                  // Stop this answer from re-animating on subsequent renders
                                  setAnimatingAnswers(prev => {
                                    const newSet = new Set(prev);
                                    newSet.delete(answerKey);
                                    return newSet;
                                  });
                                }
                              }}
                            >
                              <span className={cn("truncate font-medium", answer.isOptimistic ? "text-white/80" : "text-white")}>
                                {playerName}
                                {answer.isOptimistic && <span className="ml-1 text-xs opacity-60">⏳</span>}
                              </span>

                              {answer.isCorrect && enhancedAnimationData && (
                                <div className="absolute inset-0 pointer-events-none no-debug-box">
                                  <FootballFx
                                    trigger={enhancedAnimationData.trigger}
                                    streak={enhancedAnimationData.bonusLevel || 1}
                                  />
                                </div>
                              )}
                            </div>
                          );
                        })
                      )}
                    </div>



                    <div className="mt-auto border-t border-slate-700 pt-2">
                      {(() => {
                        // Check if all players have submitted answers for the current round
                        const realTimeAnswers: GameAnswer[] = Array.isArray(currentRoomGameData.current_round_answers)
                          ? currentRoomGameData.current_round_answers
                          : [];

                        const currentQuestionId = currentRoomGameData?.current_question_data?.questionId;
                        const answersForCurrentQuestion = realTimeAnswers.filter(answer =>
                          answer.questionId === currentQuestionId
                        );

                        const allPlayersSubmitted = answersForCurrentQuestion.length === playersInRoom.length;
                        const isHost = user && currentRoomGameData?.host_id === user.id;

                        // DIAGNOSTIC: Log the UI decision-making logic
                        console.log('[DIAGNOSTIC] UI Button Logic - Calculating whether to show Next Question button:', {
                          currentRoomGameData_current_round_answers: currentRoomGameData?.current_round_answers,
                          currentRoomGameData_current_round_answers_type: Array.isArray(currentRoomGameData?.current_round_answers) ? 'array' : typeof currentRoomGameData?.current_round_answers,
                          realTimeAnswers,
                          realTimeAnswersLength: realTimeAnswers.length,
                          currentQuestionId,
                          answersForCurrentQuestion,
                          answersForCurrentQuestionLength: answersForCurrentQuestion.length,
                          playersInRoomLength: playersInRoom.length,
                          allPlayersSubmitted,
                          isHost,
                          willShowNextQuestionButton: allPlayersSubmitted && isHost,
                          currentRoomStatus: currentRoomGameData?.status
                        });

                        return (
                          <>
                            {allPlayersSubmitted && isHost ? (
                              <>
                                <p className="text-xs text-green-400 text-center mb-2">All players submitted! Ready for next question.</p>
                                <Button
                                  onClick={handleNextQuestion}
                                  disabled={isAdvancingToNextQuestion}
                                  className="w-full mb-2 bg-blue-600 hover:bg-blue-700 text-white font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                  {isAdvancingToNextQuestion ? "Loading..." : "Next Question"}
                                </Button>
                              </>
                            ) : (
                              <p className="text-xs text-gray-500 text-center">
                                {allPlayersSubmitted
                                  ? "Waiting for host to continue..."
                                  : `Game in Progress! (${answersForCurrentQuestion.length}/${playersInRoom.length} submitted)`
                                }
                              </p>
                            )}
                            <Button
                              onClick={handleLeaveRoom}
                              className="w-full mt-3 bg-red-600 hover:bg-red-700"
                            >
                              Leave Game
                            </Button>
                          </>
                        );
                      })()}
                    </div>
                  </>
                ) : (
                  // Fallback for 'finished' or other states
                  <div className="flex flex-col h-full">
                    <div className="flex-1 flex items-center justify-center">
                      <p className="text-gray-400">
                        {currentRoomGameData?.status === 'finished' ? 'Game Finished!' : 'Loading room state...'}
                      </p>
                    </div>
                    <div className="mt-auto border-t border-slate-700 pt-2">
                      <Button
                        onClick={handleLeaveRoom}
                        className="w-full mt-3 bg-red-600 hover:bg-red-700"
                      >
                        Leave Game
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            ) : null}
          </div>
        </div>
      </div>

      {/* Global Animations - rendered through portals at root level */}
      {globalAnimations.map(animation => {
        if (animation.type === 'enhanced') {
          return (
            <GlobalMultiplayerScoreAnimation
              key={animation.id}
              trigger={animation.trigger}
              scoreIncrease={animation.scoreIncrease || 10}
              bonusLevel={animation.bonusLevel || 0}
              originPosition={animation.originPosition}
            />
          );
        } else {
          return (
            <GlobalFallbackAnimation
              key={animation.id}
              animationKey={animation.id}
              originPosition={animation.originPosition}
            />
          );
        }
      })}
    </main>
  );
}

// New default export that wraps HomePageContent with Suspense
// Use dynamic import to completely disable SSR for this component
const DynamicHomePageContent = nextDynamic(() => Promise.resolve(HomePageContent), {
  ssr: false,
  loading: () => (
    <div className="flex min-h-screen items-center justify-center text-white bg-gray-900">
      <div>Loading page...</div>
    </div>
  ),
});

export default function HomePage() {
  return <DynamicHomePageContent />;
}
