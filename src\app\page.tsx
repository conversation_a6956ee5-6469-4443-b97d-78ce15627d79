import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useEffect, useState, Suspense, useCallback, useRef, useMemo } from 'react';
import { motion } from 'framer-motion';
import { flushSync } from 'react-dom';
import { createBrowserClient } from '@supabase/ssr';
import { User, RealtimeChannel } from '@supabase/supabase-js';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useGameStore } from '@/stores/gameStore';
import { PlayerImageDisplay } from '@/components/game/PlayerImageDisplay';
import { ChoiceButton } from '@/components/game/ChoiceButton';
import { RecentAnswersList } from '@/components/game/RecentAnswersList';
import { PlayerInfoPanel } from '@/components/game/PlayerInfoPanel';
import { LeaderboardPanel } from '@/components/game/LeaderboardPanel';
import { ScorePopup } from '@/components/game/ScorePopup';
import { FootballFx } from '@/components/game/FootballFx';
import { GlobalMultiplayerScoreAnimation } from '@/components/game/GlobalMultiplayerScoreAnimation';
import { GlobalFallbackAnimation } from '@/components/game/GlobalFallbackAnimation';
import { useElementPosition } from '@/hooks/useElementPosition';
import { useOnlineStatus } from '@/hooks/useOnlineStatus';
import { DisconnectedOverlay } from '@/components/ui/DisconnectedOverlay';
import AuthModal from '@/components/auth/AuthModal';
import { supabase } from '@/lib/supabaseClient';
import type { PlayerData, GameModeType } from '@/types';
import React from 'react';

type OverallGameType = 'single-player' | 'multiplayer';
type MultiplayerPanelState = 'lobby_list' | 'in_room';
type CenterPanelMpState = 'lobby_list_detail' | 'expanded_leaderboard' | 'mp_game_active';
type MultiplayerGameMode = 'competitive' | 'cooperative';
type ConnectionStatus = 'INITIALIZING' | 'CONNECTED' | 'RECONNECTING' | 'OFFLINE';

// Define Leaderboard types
interface LeaderboardEntry {
  rank: number;
  username: string;
  score: number;
  userId?: string;
}

interface RegionalLeaderboard {
  regionName: string;
  entries: LeaderboardEntry[];
}

// Update the game player types to use a single consistent type
type GamePlayer = {
  user_id: string;
  is_connected: boolean;
  is_ready?: boolean;
  profile?: { username: string | null } | null;
};

// Type definitions
interface GameRoom {
  id: string;
  created_at: string;
  status: 'waiting' | 'active' | 'finished';
  host_id: string;
  multiplayer_mode: string;
  title: string;
  room_code: string;
  max_players?: number;
  profiles?: { username: string | null } | null;
  game_players?: any[];
  player_count?: number;
  connected_players?: number;
  current_question_data?: any;
  current_round_number?: number;
  current_round_ends_at?: string;
  player_scores?: Record<string, number>;
  current_round_answers?: Record<string, any>;
  last_activity_timestamp?: string;
}

type PlayerInRoom = {
  user_id: string;
  profile: { username: string | null } | null;
  is_ready?: boolean;
  is_connected?: boolean;
};

// Add type definition for GameAnswer if not already present
type GameAnswer = {
  choiceName: string;
  timestamp: string;
  isCorrect: boolean;
  questionId: string;
};

// Add interface for enhanced animation data
interface PlayerAnimationData {
  trigger: number;
  scoreIncrease: number;
  bonusLevel: number;
  questionId: string;
}

interface EnhancedAnimatedAnswersState {
  [userId: string]: PlayerAnimationData;
}

function HomePageContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );
  
  // State declarations
  const [user, setUser] = useState<User | null>(null);
  const [activeRoomId, setActiveRoomId] = useState<string | null>(null);
  const [playersInRoom, setPlayersInRoom] = useState<PlayerInRoom[]>([]);
  const [isLoadingPlayers, setIsLoadingPlayers] = useState(false);
  const [isRoomLoading, setIsRoomLoading] = useState<boolean>(true);
  const [errorMp, setErrorMp] = useState<string | null>(null);
  const [multiplayerPanelState, setMultiplayerPanelState] = useState<'lobby_list' | 'in_room'>('lobby_list');
  const [centerPanelMpState, setCenterPanelMpState] = useState<'lobby_list_detail' | 'mp_game_active' | 'expanded_leaderboard'>('lobby_list_detail');
  const [selectedRoomForDetail, setSelectedRoomForDetail] = useState<GameRoom | null>(null);
  const [currentRoomGameData, setCurrentRoomGameData] = useState<GameRoom | null>(null);
  const [isStartingGame, setIsStartingGame] = useState(false);
  const [showAuthForm, setShowAuthForm] = useState(false);
  const [gameRooms, setGameRooms] = useState<GameRoom[]>([]);
  const [isLoadingRooms, setIsLoadingRooms] = useState(false);
  const [animatedAnswers, setAnimatedAnswers] = useState<Record<string, Set<string>>>({});
  const [animatedCorrectAnswers, setAnimatedCorrectAnswers] = useState<Record<string, Set<string>>>({});
  const [selectedOverallGameType, setSelectedOverallGameType] = useState<OverallGameType>('single-player');
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>('INITIALIZING');
  const [userProfile, setUserProfile] = useState<{ username: string | null } | null>(null);
  const [loadingUser, setLoadingUser] = useState(true);
  const [hasMounted, setHasMounted] = useState(false);
  const [isCreatingRoom, setIsCreatingRoom] = useState(false);
  const [isJoiningOrRejoiningRoom, setIsJoiningOrRejoiningRoom] = useState(false);
  const [isSubmittingReady, setIsSubmittingReady] = useState(false);
  const [isCreatingAndJoiningRoom, setIsCreatingAndJoiningRoom] = useState(false);
  const [lobbyFetchError, setLobbyFetchError] = useState<string | null>(null);

  // Layer 1: The Proactive Client - Track browser online status
  const isOnline = useOnlineStatus();

  // Add state for global animations
  const [globalAnimations, setGlobalAnimations] = useState<Array<{
    id: string;
    type: 'enhanced' | 'fallback';
    trigger: number;
    scoreIncrease: number;
    bonusLevel: number;
    originPosition: { x: number; y: number };
  }>>([]);

// Connection Status Indicator Component
const ConnectionStatusIndicator = ({ status }: { status: ConnectionStatus }) => {
  switch (status) {
    // In the ideal state, render nothing at all.
    case 'CONNECTED':
    case 'INITIALIZING':
      return null;

    // When reconnecting, show a calm, temporary message.
    case 'RECONNECTING':
      return (
        <div className="reconnecting-indicator flex items-center justify-center gap-2 bg-yellow-900/50 text-yellow-200 p-2 rounded text-xs mb-3 text-center w-full border border-yellow-600/30">
          <div className="spinner w-4 h-4 border-2 border-yellow-400 border-t-transparent rounded-full animate-spin"></div>
          <span>Reconnecting...</span>
        </div>
      );

    // When offline, show a more prominent message.
    case 'OFFLINE':
      return (
        <div className="offline-indicator flex items-center justify-center gap-2 bg-red-900/50 text-red-200 p-2 rounded text-xs mb-3 text-center w-full border border-red-600/30">
          <div className="w-4 h-4 rounded-full bg-red-400"></div>
          <span>Connection Lost</span>
        </div>
      );

    default:
      return null;
  }
};

  // Add state for enhanced animation data
  const [enhancedAnimatedAnswers, setEnhancedAnimatedAnswers] = useState<EnhancedAnimatedAnswersState>({});

  // Add the missing state declaration for hasAnswered
  const [hasAnswered, setHasAnswered] = useState(false);

  // Add state for answer submission
  const [isSubmittingAnswer, setIsSubmittingAnswer] = useState(false);
  const [hasSubmittedCurrentRound, setHasSubmittedCurrentRound] = useState(false);

  // Add optimistic answer state for snappy UI updates
  const [optimisticAnswer, setOptimisticAnswer] = useState<{
    userId: string;
    questionId: string;
    choiceName: string;
    timestamp: number;
    isCorrect: boolean;
    isPending: boolean;
  } | null>(null);

  // Define fetchAndSetGameRooms first since it's used by other functions
  const fetchAndSetGameRooms = async () => {
    setIsLoadingRooms(true);
    setErrorMp(null);
    try {
      console.log("[LOBBY_FETCH] Attempting game_rooms SELECT with PROFILES join and PLAYER count");
      const { data, error } = await supabase
        .from('game_rooms')
        .select(`
          id,
          title,
          status,
          room_code,
          multiplayer_mode,
          host_id,
          created_at,
          max_players,
          profiles:host_id (
            username
          ),
          game_players (
            user_id,
            is_connected
          )
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error("[LOBBY_FETCH] Error fetching game_rooms with joins:", JSON.stringify(error, null, 2));
        setErrorMp(`Failed to fetch room details: ${error.message}`);
        setGameRooms(prevRooms => prevRooms || []);
      } else {
        console.log("[LOBBY_FETCH] Successfully fetched game_rooms with joins:", data);
        const transformedData: GameRoom[] = (data || []).map(room => {
          const connectedPlayerCount = room.game_players?.filter(p => p.is_connected).length || 0;
          return {
            id: room.id,
            created_at: room.created_at,
            status: room.status,
            host_id: room.host_id,
            multiplayer_mode: room.multiplayer_mode,
            title: room.title,
            room_code: room.room_code,
            max_players: room.max_players,
            profiles: room.profiles ? { username: room.profiles[0]?.username } : null,
            game_players: room.game_players,
            player_count: room.game_players?.length || 0,
            connected_players: connectedPlayerCount
          };
        });
        setGameRooms(transformedData);
      }
    } catch (e: any) {
      console.error("[LOBBY_FETCH] Exception fetching game_rooms:", e);
      setErrorMp(`Exception fetching rooms: ${e.message}`);
      setGameRooms(prevRooms => prevRooms || []);
    } finally {
      setIsLoadingRooms(false);
    }
  };

  // Define fetchPlayersInActiveRoom
  const fetchPlayersInActiveRoom = async (roomId: string) => {
    if (!roomId) return;
    setIsLoadingPlayers(true);
    console.log(`[RoomView] Fetching players for room: ${roomId}`);
    try {
      const { data, error } = await supabase
        .from('game_players')
        .select(`
          user_id,
          is_ready,
          is_connected,
          profile:profiles (
            username
          )
        `)
        .eq('room_id', roomId);

      if (error) {
        console.error("[RoomView] Error fetching players in room:", JSON.stringify(error, null, 2));
        setPlayersInRoom([]);
        setErrorMp(`Failed to fetch players: ${error.message}`);
      } else {
        console.log("[RoomView] Successfully fetched players in room:", data);
        const transformedData: PlayerInRoom[] = (data || []).map(player => ({
          user_id: player.user_id,
          is_ready: player.is_ready,
          is_connected: player.is_connected,
          profile: player.profile ? { username: player.profile[0]?.username } : null
        }));
        setPlayersInRoom(transformedData);
      }
    } catch (e: any) {
      console.error("[RoomView] Exception fetching players:", e);
      setPlayersInRoom([]);
      setErrorMp(`Exception fetching players: ${e.message}`);
    } finally {
      setIsLoadingPlayers(false);
    }
  };

  // DEFINITIVE SOLUTION: Comprehensive room initialization function
  const initializeRoomView = async (roomId: string) => {
    console.log(`[RoomView] Initializing view for room: ${roomId}`);
    // 1. Set loading to true at the very start.
    setIsRoomLoading(true);

    try {
      // 2. Fetch all critical data in parallel.
      const [playersResponse, roomResponse] = await Promise.all([
        supabase.from('game_players').select('*, profiles(*)').eq('room_id', roomId),
        supabase.from('game_rooms').select('*').eq('id', roomId).single(),
      ]);

      if (playersResponse.error) throw playersResponse.error;
      if (roomResponse.error) throw roomResponse.error;

      // 3. Update your component's state with the fetched data.
      const transformedData: PlayerInRoom[] = (playersResponse.data || []).map(player => ({
        user_id: player.user_id,
        is_ready: player.is_ready,
        is_connected: player.is_connected,
        profile: player.profile ? { username: player.profile[0]?.username } : null
      }));
      setPlayersInRoom(transformedData);
      setCurrentRoomGameData(roomResponse.data);

    } catch (error) {
      console.error('[RoomView] Failed to initialize room view:', error);
      setErrorMp('Could not load game room.');
      // Handle error, maybe kick user back to lobby
      setActiveRoomId(null);
    } finally {
      // 4. CRITICAL: In the finally block, set loading to false.
      // This guarantees it runs whether the fetch succeeded or failed.
      console.log(`[RoomView] Finished initializing. Setting loading to false.`);
      setIsRoomLoading(false);
    }
  };

  // Authentication functions
  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut();
      setUser(null);
      setUserProfile(null);
      setActiveRoomId(null);
      setMultiplayerPanelState('lobby_list');
      setCenterPanelMpState('lobby_list_detail');
      setSelectedRoomForDetail(null);
      setPlayersInRoom([]);
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  // Define handleJoinRoom
  const handleJoinRoom = async (roomId: string, autoJoinedAfterCreate = false) => {
    if (!user) {
      setErrorMp("You must be logged in to join or rejoin a room.");
      setShowAuthForm(true);
      return;
    }
    setErrorMp(null);
    console.log(`[Client] User ${user.id} attempting to join/rejoin room: ${roomId}`);

    try {
      const { data: roomData, error: roomError } = await supabase
        .from('game_rooms')
        .select('id, status, max_players, game_players(user_id, is_connected)')
        .eq('id', roomId)
        .single();

      if (roomError || !roomData) {
        console.error("[Client] Error fetching room details or room not found:", roomError);
        setErrorMp(`Room not found or error fetching details: ${roomError?.message || 'Unknown error'}`);
        return;
      }

      const existingPlayerEntry = roomData.game_players.find(p => p.user_id === user.id);

      if (existingPlayerEntry) {
        // Player has an entry in the room
        if (existingPlayerEntry.is_connected) {
          console.log(`[Client] User ${user.id} is already connected to room ${roomId}. Proceeding to room view.`);
        } else {
          // Player was disconnected, attempt to rejoin (allow rejoin for 'waiting' OR 'active' status)
          if (roomData.status === 'waiting' || roomData.status === 'active') {
            console.log(`[Client] User ${user.id} attempting to REJOIN ${roomData.status} room ${roomId}.`);
            const { error: rejoinError } = await supabase
              .from('game_players')
              .update({
                is_connected: true,
                last_seen_at: new Date().toISOString()
                // Do NOT automatically set is_ready to true here. They should ready up again if the game is waiting.
              })
              .match({ room_id: roomId, user_id: user.id });

            if (rejoinError) {
              console.error("[Client] Error rejoining room (updating player status):", rejoinError);
              setErrorMp(`Failed to rejoin room: ${rejoinError.message}`);
              return;
            }
            console.log(`[Client] User ${user.id} successfully REJOINED room ${roomId}.`);
          } else {
            setErrorMp("Cannot rejoin: this game has finished.");
            return;
          }
        }
      } else {
        // Player is not in the room, attempt new join
        console.log(`[Client] User ${user.id} attempting NEW JOIN to room ${roomId}.`);
        if (roomData.status !== 'waiting') {
          setErrorMp("Cannot join: this game has already started or finished and you were not originally part of it.");
          return;
        }

        // Check connected player count against max_players from roomData
        const connectedPlayerCount = roomData.game_players.filter(p => p.is_connected).length;
        if (roomData.max_players != null && connectedPlayerCount >= roomData.max_players) {
          setErrorMp("Cannot join: room is full.");
          return;
        }

        const { error: joinError } = await supabase
          .from('game_players')
          .insert({
            room_id: roomId,
            user_id: user.id,
            is_connected: true,
            last_seen_at: new Date().toISOString()
          });

        if (joinError) {
          console.error("[Client] Error during new game_players insert:", JSON.stringify(joinError, null, 2));
          if (joinError.message.includes('unique_player_per_room')) {
            console.warn("[Client] Race condition or stale data? User already in room, attempting to mark as connected.");
            const { error: updateError } = await supabase
              .from('game_players')
              .update({ is_connected: true, last_seen_at: new Date().toISOString() })
              .match({ room_id: roomId, user_id: user.id });
            if (updateError) {
              setErrorMp(`Failed to join (update): ${updateError.message}`);
              return;
            }
          } else {
            setErrorMp(`Failed to join: ${joinError.message}`);
            return;
          }
        }
        console.log(`[Client] User ${user.id} successfully made NEW JOIN to room ${roomId}.`);
      }

      setActiveRoomId(roomId);
      setMultiplayerPanelState('in_room');
      setCenterPanelMpState('mp_game_active');
      setSelectedRoomForDetail(null);
      // No need to call fetchAndSetGameRooms() here unless it's autoJoinedAfterCreate,
      // because the realtime subscription for the room itself should update currentRoomGameData.
      if (autoJoinedAfterCreate) {
        await fetchAndSetGameRooms();
      }

    } catch (e: any) {
      console.error('[Client] Exception during room join/rejoin process:', JSON.stringify(e, null, 2));
      setErrorMp(`Failed to join/rejoin room: ${e.message}`);
    }
  };

  // Define handleLeaveRoom
  const handleLeaveRoom = async () => {
    if (!user || !activeRoomId) return;

    try {
      console.log("[Client] Attempting to leave room via Edge Function:", { roomId: activeRoomId, userId: user.id });

      const { data, error: invokeError } = await supabase.functions.invoke('leave-room-handler', {
        body: { roomId: activeRoomId },
      });

      if (invokeError) {
        console.error("[Client] Error invoking leave-room-handler:", invokeError);
        let errorMessage = 'Failed to leave room.';
        if (invokeError.context && invokeError.context.json) {
          errorMessage = invokeError.context.json.error || invokeError.message;
        } else {
          errorMessage = invokeError.message || 'Unknown server error';
        }
        setErrorMp(errorMessage);
      } else {
        console.log("[Client] Successfully left room via Edge Function:", data);
        // Reset client state
        setActiveRoomId(null);
        setMultiplayerPanelState('lobby_list');
        setCenterPanelMpState('lobby_list_detail');
        setSelectedRoomForDetail(null);
        setPlayersInRoom([]);
        // Refresh the lobby list
        await fetchAndSetGameRooms();
      }
    } catch (e: any) {
      console.error("[Client] Exception during room leave:", e);
      setErrorMp(`Exception during room leave: ${e.message}`);
      
      // Attempt to reset UI even on general exception
      setActiveRoomId(null);
      setMultiplayerPanelState('lobby_list');
      setCenterPanelMpState('lobby_list_detail');
      setSelectedRoomForDetail(null);
      setPlayersInRoom([]);
      await fetchAndSetGameRooms();
    }
  };

  // Define handleCreateRoom
  const handleCreateRoom = async () => {
    if (!user) {
      setErrorMp("You must be logged in to create a room.");
      setShowAuthForm(true);
      return;
    }

    setIsCreatingRoom(true);
    setErrorMp(null);

    try {
      const { data, error } = await supabase.functions.invoke('create-room-handler', {
        body: {
          title: `${userProfile?.username || 'Your'}'s Game`,
          multiplayerMode: 'competitive',
          maxPlayers: 4
        },
      });

      if (error) {
        console.error("[Client] Error creating room:", error);
        setErrorMp(`Failed to create room: ${error.message}`);
        return;
      }

      console.log("[Client] Room created successfully:", data);
      const roomId = data.roomId;

      // Auto-join the created room
      setIsCreatingAndJoiningRoom(true);
      await handleJoinRoom(roomId, true);

      // THE FIX: Immediately fetch the player list for the new room so the host sees themselves
      console.log('[Client] [CREATE_ROOM] Fetching initial player list for host after auto-join');
      await fetchPlayersInActiveRoom(roomId, 'host_create');

      await fetchAndSetGameRooms();

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      console.error('[Client] [CREATE_ROOM] Exception during room creation:', err);
      setErrorMp(`Exception creating room: ${err.message}`);
    } finally {
      setIsCreatingRoom(false);
      setIsCreatingAndJoiningRoom(false);
    }
  };

  // Define handleStartGame
  const handleStartGame = async () => {
    if (isStartingGame) {
      console.warn("[Client] Start game request already in progress - blocking duplicate request.");
      return;
    }

    if (!user || !activeRoomId) {
      setErrorMp("Missing user or room information.");
      return;
    }

    // CRITICAL FIX: Fetch fresh room details from server to check current status
    console.log("[Client] [START_GAME] Fetching latest room details from server before starting...");
    let currentRoomDetails;
    try {
      const { data: freshRoomData, error: roomFetchError } = await supabase
        .from('game_rooms')
        .select('*')
        .eq('id', activeRoomId)
        .single();

      if (roomFetchError || !freshRoomData) {
        console.error("[Client] Failed to fetch current room details", roomFetchError);
        setErrorMp("Failed to fetch room details. Cannot start game.");
        return;
      }

      currentRoomDetails = freshRoomData;
      console.log("[Client] [START_GAME] Fresh room data fetched:", {
        roomId: activeRoomId,
        status: currentRoomDetails.status,
        hostId: currentRoomDetails.host_id
      });
    } catch (fetchError) {
      console.error("[Client] Exception fetching room details:", fetchError);
      setErrorMp("Exception fetching room details. Cannot start game.");
      return;
    }

    // CRITICAL FIX: Handle case where game has already started (state synchronization)
    if (currentRoomDetails.status !== 'waiting') {
      console.warn(`[Client] [STATE_SYNC_FIX] Attempted to start a game that is not in 'waiting' status (current: ${currentRoomDetails.status}). Forcing state synchronization.`);

      // The game is already active. The client is out of sync.
      // Force the client to catch up to the correct state.
      setCurrentRoomGameData(currentRoomDetails); // Update state with the correct room data

      // The UI will now re-render based on the correct status,
      // which should show the game view instead of the lobby.
      console.log(`[Client] [STATE_SYNC_FIX] State synchronization complete. UI should now reflect status: ${currentRoomDetails.status}`);

      return; // Stop the function here - don't set isStartingGame since we're not actually starting
    }

    setIsStartingGame(true);
    setErrorMp(null);

    try {
      const { data, error: invokeError } = await supabase.functions.invoke('start-game-handler', {
        body: { roomId: activeRoomId },
      });

      if (invokeError) {
        console.error("[Client] Error invoking start-game-handler:", invokeError);
        setErrorMp('Failed to start game.');
      } else {
        console.log("[Client] Game started successfully:", data);
      }
    } catch (error) {
      console.error("[Client] Exception during game start:", error);
      setErrorMp(`Exception starting game: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsStartingGame(false);
    }
  };

  // Define handleToggleReady
  const handleToggleReady = async () => {
    if (!user || !activeRoomId || isSubmittingReady) return;

    setIsSubmittingReady(true);
    setErrorMp(null);

    try {
      const currentPlayer = playersInRoom.find(p => p.user_id === user.id);
      const newReadyState = !currentPlayer?.is_ready;

      const { error } = await supabase
        .from('game_players')
        .update({ is_ready: newReadyState })
        .match({ room_id: activeRoomId, user_id: user.id });

      if (error) {
        console.error("[Client] Error updating ready state:", error);
        setErrorMp(`Failed to update ready state: ${error.message}`);
      }
    } catch (error) {
      console.error("[Client] Exception updating ready state:", error);
      setErrorMp(`Exception updating ready state: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsSubmittingReady(false);
    }
  };

  // Add this useEffect after the other useEffect hooks
  useEffect(() => {
    // Set up global listener for game_players changes
    const gamePlayersGlobalListener = supabase
      .channel('public-game-players-changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'game_players' },
        (payload) => {
          console.log('[Realtime] Global game_players change detected:', payload);

          // THE FIX: If the user is already in a room, don't refresh the lobby.
          if (activeRoomId) {
            console.log('[Realtime] Ignoring global game_players change because user is in an active room.');
            return;
          }

          // Debounce the fetch to avoid too many rapid updates
          const timeoutId = setTimeout(() => {
            console.log('[Realtime] Refreshing lobby list after game_players change');
            fetchAndSetGameRooms();
          }, 500); // 500ms debounce

          return () => clearTimeout(timeoutId);
        }
      )
      .subscribe((status, err) => {
        if (status === 'SUBSCRIBED') {
          console.log('[Realtime] Subscribed to global game_players changes');
        }
        if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
          console.error('[Realtime] Error subscribing to global game_players changes:', err || 'Unknown error');
        }
        if (err) {
          console.error('[Realtime] Error subscribing to global game_players changes:', err);
        }
      });

    // Set up global listener for game_rooms changes (especially DELETE events for stale room cleanup)
    const gameRoomsGlobalListener = supabase
      .channel('public-game-rooms-changes')
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'game_rooms' },
        (payload) => {
          console.log('[Realtime] Global game_rooms change detected:', payload);

          // THE FIX: If the user is already in a room, don't refresh the lobby.
          if (activeRoomId) {
            console.log('[Realtime] Ignoring global game_rooms change because user is in an active room.');
            return;
          }

          // Debounce the fetch to avoid too many rapid updates
          const timeoutId = setTimeout(() => {
            console.log('[Realtime] Refreshing lobby list after game_rooms change');
            fetchAndSetGameRooms();
          }, 500); // 500ms debounce

          return () => clearTimeout(timeoutId);
        }
      )
      .subscribe((status, err) => {
        if (status === 'SUBSCRIBED') {
          console.log('[Realtime] Subscribed to global game_rooms changes');
        }
        if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT') {
          console.error('[Realtime] Error subscribing to global game_rooms changes:', err || 'Unknown error');
        }
        if (err) {
          console.error('[Realtime] Error subscribing to global game_rooms changes:', err);
        }
      });

    return () => {
      if (gamePlayersGlobalListener) {
        supabase.removeChannel(gamePlayersGlobalListener);
        console.log('[Realtime] Unsubscribed from global game_players changes');
      }
      if (gameRoomsGlobalListener) {
        supabase.removeChannel(gameRoomsGlobalListener);
        console.log('[Realtime] Unsubscribed from global game_rooms changes');
      }
    };
  }, [activeRoomId]); // Include activeRoomId in dependencies so the listeners can access the current value

  // Effect for multiplayer animations (replacing the problematic useEffect that contains line 982)
  useEffect(() => {
    // Guard conditions: Ensure we are in the right state to process animations
    if (
      selectedOverallGameType !== 'multiplayer' ||
      !currentRoomGameData ||
      currentRoomGameData.status !== 'active' ||
      !currentRoomGameData.current_round_answers || // Must exist
      typeof currentRoomGameData.current_round_answers !== 'object' || // Must be an object
      !currentRoomGameData.current_question_data ||
      !currentRoomGameData.current_question_data.questionId || // Question ID must exist
      !user || !user.id // User must exist
    ) {
      // console.log('[Animation Effect] Guards not met, returning.');
      return; 
    }

    const currentQuestionId = currentRoomGameData.current_question_data.questionId;
    const allSubmittedAnswersObject = currentRoomGameData.current_round_answers; 
    
    // If the answers object is literally empty, no need to proceed further.
    if (Object.keys(allSubmittedAnswersObject).length === 0) {
      // console.log('[Animation Effect] current_round_answers object is empty.');
      return;
    }

    // --- THIS IS THE CRITICAL FIX ---
    // Convert the 'allSubmittedAnswersObject' (which is a Record/object)
    // into an array of its entries [userId, answerData] to iterate over.
    const answerEntriesArray: [string, { choiceName: string; timestamp: string; isCorrect: boolean; questionId: string }][] = Object.entries(allSubmittedAnswersObject);

    const previouslyAnimatedForThisQuestion = animatedCorrectAnswers[currentQuestionId] || new Set<string>();
    const newlyAnimatedUserIdsThisPass = new Set<string>();

    // console.log(`[Animation Effect] Processing Q: ${currentQuestionId}, Previous Animated:`, previouslyAnimatedForThisQuestion);

    // Iterate over the ARRAY of answer entries
    answerEntriesArray.forEach(([userId, answerData]) => {
      if (
        answerData.questionId === currentQuestionId && // Ensure answer is for the current question
        answerData.isCorrect &&
        !previouslyAnimatedForThisQuestion.has(userId) // Check if THIS user's correct answer for THIS question was already animated
      ) {
        console.log(`[Animation] Triggering for NEW correct MP answer: User ${userId} on Q ${currentQuestionId}.`);
        
        // Trigger animation for the current user if they got it right
        if (userId === user.id) { 
            useGameStore.setState((state) => ({
                lastScoreChange: 10, // Define your scoring logic
                animationTrigger: state.animationTrigger + 1,
            }));
        }
        // (Future enhancement: trigger targeted animations for other players)
        
        newlyAnimatedUserIdsThisPass.add(userId);
      }
    });

    // If any new animations were identified in this pass, update the tracking state
    if (newlyAnimatedUserIdsThisPass.size > 0) {
      setAnimatedCorrectAnswers(prev => {
        const updatedSetForQuestion = new Set([...previouslyAnimatedForThisQuestion, ...newlyAnimatedUserIdsThisPass]);
        return { 
          ...prev, 
          [currentQuestionId]: updatedSetForQuestion 
        };
      });
    }

  }, [
      currentRoomGameData, // Depends on the whole object as multiple fields are used
      user?.id, 
      selectedOverallGameType,
      animatedCorrectAnswers, // To read previously animated and to update it
  ]);

  // Effect to reset animated answers when question changes
  useEffect(() => {
      if (currentRoomGameData?.current_question_data?.questionId) {
          const currentQId = currentRoomGameData.current_question_data.questionId;
          setAnimatedCorrectAnswers(prev => {
              // Initialize an empty Set for the new question if it's not already there.
              // This ensures that on the first pass of the animation effect for a new question,
              // previouslyAnimatedForThisQuestion will be an empty Set.
              if (!prev[currentQId]) {
                   return { ...prev, [currentQId]: new Set() };
              }
              return prev; 
          });
      }
  }, [currentRoomGameData?.current_question_data?.questionId]); // Only depends on questionId changing

  // Add handleMultiplayerAnswerSubmit function - THE DEFINITIVE SOLUTION
  const handleMultiplayerAnswerSubmit = async (choiceName: string) => {
    // Prevent multiple answers for the same question
    setHasAnswered(true);

    const questionData = currentRoomGameData?.current_question_data;
    if (!questionData || !user) return;

    const selectedChoice = questionData.choices.find(choice => choice.name === choiceName);
    if (!selectedChoice) return;

    // --- THE DEFINITIVE, INSTANTANEOUS FEEDBACK LOGIC ---

    // 1. Create the data for the optimistic answer card.
    const optimisticAnswerData = {
      userId: user.id,
      questionId: questionData.questionId,
      choiceName: choiceName,
      timestamp: Date.now(),
      isCorrect: selectedChoice.isCorrect,
      isPending: true,
    };

    // 2. Use flushSync to force a synchronous render of ALL state updates queued within it.
    flushSync(() => {
      // Queue the state update for the optimistic card to slide in.
      setOptimisticAnswer(optimisticAnswerData);

      // Queue the state update for the football animation IF THE ANSWER IS CORRECT.
      // By placing this inside flushSync, it's guaranteed to render at the same time as the card.
      if (selectedChoice.isCorrect) {
        console.log('[ANIMATION] CORRECT. Triggering card and football effect INSTANTLY.');
        setEnhancedAnimatedAnswers(prev => ({
          ...prev,
          [user.id]: {
            trigger: Date.now() + Math.random(),
            scoreIncrease: 10,
            bonusLevel: 1,
            questionId: questionData.questionId,
          }
        }));
      }
    });

    // --- END OF DEFINITIVE FIX ---

    // 3. AFTER the UI has been updated instantly, send the answer to the server.
    try {
      const { data, error } = await supabase.functions.invoke('submit-answer-handler', {
        body: {
          roomId: activeRoomId,
          choiceName: choiceName
        },
      });

      if (error) {
        console.error('[Client] Error submitting answer:', error);
        // Optional: Revert optimistic UI on error
        setOptimisticAnswer(null);
        return;
      }

      console.log('[Client] Answer submitted successfully to server:', data);
    } catch (error) {
      console.error('Error submitting answer:', error);
      // Optional: Revert optimistic UI on error
      setOptimisticAnswer(null);
    }
  };

  // DEFINITIVE SOLUTION: Room initialization useEffect
  useEffect(() => {
    // We only care about this effect if we have an active room.
    if (!activeRoomId) {
      setIsRoomLoading(false); // If there's no room, we're not loading one.
      return;
    }

    // This function will fetch EVERYTHING needed to display the room.
    const initializeRoom = async () => {
      console.log(`[RoomView] Initializing view for room: ${activeRoomId}`);
      // 1. Set loading to true at the very start.
      setIsRoomLoading(true);

      try {
        // 2. Fetch all critical data in parallel.
        const [playersResponse, roomResponse] = await Promise.all([
          supabase.from('game_players').select('*, profiles(*)').eq('room_id', activeRoomId),
          supabase.from('game_rooms').select('*').eq('id', activeRoomId).single(),
        ]);

        if (playersResponse.error) throw playersResponse.error;
        if (roomResponse.error) throw roomResponse.error;

        // 3. Update your component's state with the fetched data.
        const transformedData: PlayerInRoom[] = (playersResponse.data || []).map(player => ({
          user_id: player.user_id,
          is_ready: player.is_ready,
          is_connected: player.is_connected,
          profile: player.profile ? { username: player.profile[0]?.username } : null
        }));
        setPlayersInRoom(transformedData);
        setCurrentRoomGameData(roomResponse.data);

      } catch (error) {
        console.error('[RoomView] Failed to initialize room view:', error);
        // Handle error, maybe kick user back to lobby
        setActiveRoomId(null);
      } finally {
        // 4. CRITICAL: In the finally block, set loading to false.
        // This guarantees it runs whether the fetch succeeded or failed.
        console.log(`[RoomView] Finished initializing. Setting loading to false.`);
        setIsRoomLoading(false);
      }
    };

    initializeRoom();

    // The subscription setup can be in the same hook or a separate one.
    // Its job is to handle UPDATES, not the INITIAL load.
    const channel = supabase.channel(`room-${activeRoomId}-init`).subscribe();

    return () => {
      supabase.removeAllChannels();
    };

  }, [activeRoomId, user, supabase]); // Runs when activeRoomId changes

  // Check if current user is marked as disconnected
  const self = playersInRoom.find(p => p.user_id === user?.id);
  const handleRejoinRoom = async () => {
    if (activeRoomId) {
      await handleJoinRoom(activeRoomId);
    }
  };

  // If current user is marked as disconnected, show the DisconnectedOverlay
  if (self && !self.is_connected && selectedOverallGameType === 'multiplayer' && multiplayerPanelState === 'in_room') {
    return <DisconnectedOverlay onReconnect={handleRejoinRoom} />;
  }

  return (
    <main className="flex min-h-screen flex-col items-center p-4 md:p-12 lg:p-24 pt-12 md:pt-20">
      {/* Auth Modal in top-right corner */}
      <div className="absolute top-4 right-4 z-[100]">
        <AuthModal />
      </div>

      {/* Game Type Toggle */}
      <div className="flex items-center justify-center mb-8 w-full max-w-4xl">
        {/* Single Player Button Container (always takes up space) */}
        <div className="w-[150px] md:w-[180px] flex-shrink-0 flex justify-start">
          <Button
            onClick={() => setSelectedOverallGameType('single-player')}
            className={cn(
              "px-5 py-2.5 md:px-7 md:py-3.5 text-lg md:text-xl font-bold rounded-lg",
              "border-yellow-500 border-[3px]",
              "transition-all shadow-md",
              selectedOverallGameType === 'single-player'
                ? "bg-blue-600 text-white ring-4 ring-yellow-500 ring-offset-2 ring-offset-green-950"
                : "bg-blue-700 text-gray-200 hover:bg-blue-600"
            )}
          >
            Single Player
          </Button>
        </div>

        {/* Center Title */}
        <div className="flex-1 text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-yellow-300 mb-2" style={{textShadow: '3px 3px 5px rgba(0,0,0,0.7)'}}>
            Recognition Combine
          </h1>
          <p className="text-lg md:text-xl text-gray-300">Test your NFL player knowledge</p>
        </div>

        {/* Multiplayer Button Container (always takes up space) */}
        <div className="w-[150px] md:w-[180px] flex-shrink-0 flex justify-end">
          <Button
            onClick={() => setSelectedOverallGameType('multiplayer')}
            className={cn(
              "px-5 py-2.5 md:px-7 md:py-3.5 text-lg md:text-xl font-bold rounded-lg",
              "border-yellow-500 border-[3px]",
              "transition-all shadow-md",
              selectedOverallGameType === 'multiplayer'
                ? "bg-blue-600 text-white ring-4 ring-yellow-500 ring-offset-2 ring-offset-green-950"
                : "bg-blue-700 text-gray-200 hover:bg-blue-600"
            )}
          >
            Multiplayer
          </Button>
        </div>
      </div>

      {/* Main Game Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 w-full max-w-7xl h-[600px]">
        {/* Left Panel */}
        <div className="md:col-span-1 bg-green-900 bg-opacity-75 pt-5 px-3 pb-3 rounded-lg border border-green-700 text-white shadow-lg h-[600px] flex flex-col">
          {selectedOverallGameType === 'single-player' ? (
            <LeaderboardPanel />
          ) : multiplayerPanelState === 'lobby_list' ? (
            // Multiplayer Lobby List
            <div className="flex flex-col h-full">
              <h2 className="text-2xl font-bold mb-3 text-center text-yellow-300 border-b-2 border-yellow-500 pb-2">Game Rooms</h2>
              <ConnectionStatusIndicator status={connectionStatus} />
              {errorMp && !selectedRoomForDetail && (
                <p className="text-red-400 bg-red-900 p-2 rounded text-xs mb-3 text-center w-full">{errorMp}</p>
              )}

              {lobbyFetchError && !selectedRoomForDetail && !isLoadingRooms && (
                <div className="text-center text-red-400 bg-red-900/50 p-3 rounded text-xs mb-3 w-full">
                  <p>Could not load games. Please try again.</p>
                  <Button
                    onClick={async () => {
                      console.log("[Client] Retry fetch button clicked after error.");
                      await fetchAndSetGameRooms();
                    }}
                    className="mt-2 px-3 py-1 text-xs bg-red-600 hover:bg-red-700 text-white rounded-md"
                  >
                    Retry
                  </Button>
                </div>
              )}

              {/* Room Detail View */}
              {selectedRoomForDetail && user && (
                <div className="w-full bg-slate-800/60 p-4 rounded-lg shadow-lg text-sm">
                  <p><span className="font-semibold text-lime-300">Mode:</span> <span className="capitalize">{selectedRoomForDetail.multiplayer_mode?.replace('_',' ')}</span></p>
                  <p><span className="font-semibold text-lime-300">Host:</span> {selectedRoomForDetail.profiles?.username || 'Unknown'}</p>
                  <p><span className="font-semibold text-lime-300">Status:</span> <span className="capitalize">{selectedRoomForDetail.status}</span></p>
                  <p>
                    <span className="font-semibold text-lime-300">Players:</span> {selectedRoomForDetail.connected_players ?? 0} / {selectedRoomForDetail.max_players ?? 8}
                  </p>
                  <p className="mt-1"><span className="font-semibold text-lime-300">Code:</span> {selectedRoomForDetail.room_code || 'N/A'}</p>

                  {(() => {
                    // Check if the current user has an entry in this room's game_players
                    const playerEntryInSelectedRoom = selectedRoomForDetail.game_players?.find(
                      (gp) => gp.user_id === user.id
                    );

                    if (selectedRoomForDetail.status === 'waiting') {
                      // For waiting rooms, show Join/Rejoin if user is not connected
                      if (!playerEntryInSelectedRoom || !playerEntryInSelectedRoom.is_connected) {
                        const isFull = selectedRoomForDetail.connected_players != null &&
                                      selectedRoomForDetail.max_players != null &&
                                      selectedRoomForDetail.connected_players >= selectedRoomForDetail.max_players;

                        return (
                          <Button
                            onClick={() => handleJoinRoom(selectedRoomForDetail.id)}
                            className="w-full mt-3 bg-blue-600 hover:bg-blue-700 py-1.5"
                            disabled={isFull || isJoiningOrRejoiningRoom}
                          >
                            {isJoiningOrRejoiningRoom ? "Joining..." : (isFull ? "Room Full" : (playerEntryInSelectedRoom ? "Rejoin Waiting Room" : "Join This Room"))}
                          </Button>
                        );
                      }
                    } else if (selectedRoomForDetail.status === 'active') {
                      // For active games, show Rejoin only if user was part of it and is disconnected
                      if (playerEntryInSelectedRoom && !playerEntryInSelectedRoom.is_connected) {
                        return (
                          <Button
                            onClick={() => handleJoinRoom(selectedRoomForDetail.id)}
                            className="w-full mt-3 bg-green-600 hover:bg-green-700 py-1.5"
                            disabled={isJoiningOrRejoiningRoom}
                          >
                            {isJoiningOrRejoiningRoom ? "Rejoining..." : "Rejoin Active Game"}
                          </Button>
                        );
                      }
                    } else if (selectedRoomForDetail.status === 'finished') {
                      return <p className="text-center text-gray-400 mt-3">This game has finished.</p>;
                    }
                    return null; // No button if user is already connected, or game is active and user wasn't part of it
                  })()}

                  <Button
                    onClick={() => setSelectedRoomForDetail(null)}
                    className="w-full mt-2 bg-gray-600 hover:bg-gray-500 text-xs py-1"
                  >
                    Back to List
                  </Button>
                </div>
              )}

              {/* Room List */}
              {!selectedRoomForDetail && (
                <div className="flex-1 overflow-y-auto">
                  {isLoadingRooms ? (
                    <div className="text-center text-gray-400 py-4">Loading rooms...</div>
                  ) : gameRooms.length === 0 ? (
                    <div className="text-center text-gray-400 py-4">No active rooms</div>
                  ) : (
                    gameRooms.map(room => (
                      <div
                        key={room.id}
                        onClick={() => setSelectedRoomForDetail(room)}
                        className="cursor-pointer p-2 mb-2 bg-slate-700/50 hover:bg-slate-700/70 rounded text-xs transition-colors"
                      >
                        <div className="flex justify-between items-center">
                          <span className="font-semibold truncate">{room.title}</span>
                          <span className={cn(
                            "px-1.5 py-0.5 rounded text-xs",
                            room.status === 'waiting' ? "bg-yellow-600" :
                            room.status === 'active' ? "bg-green-600" : "bg-gray-600"
                          )}>
                            {room.status}
                          </span>
                        </div>
                        <div className="text-gray-400 mt-1">
                          {room.connected_players ?? 0}/{room.max_players ?? 8} players
                        </div>
                      </div>
                    ))
                  )}
                </div>
              )}

              {/* Create Room Button */}
              {!selectedRoomForDetail && (
                <div className="mt-auto pt-3 border-t border-green-700">
                  <Button
                    onClick={handleCreateRoom}
                    disabled={!user || isLoadingRooms || isCreatingRoom || !isOnline}
                    title={!isOnline ? "You are offline. Please check your connection." : "Create a new game"}
                    className={cn(
                      "w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-1.5 text-sm rounded shadow-md",
                      "transition-all duration-200",
                      isCreatingRoom && "opacity-75 cursor-not-allowed"
                    )}
                  >
                    {isCreatingRoom ? "Creating..." : isLoadingRooms ? "..." : "Host Game"}
                  </Button>
                </div>
              )}
            </div>
          ) : (
            // In Room View
            <div className="flex flex-col h-full">
              <div className="flex items-center justify-between mb-3 pb-2 border-b border-yellow-500">
                <h2 className="text-xl font-bold text-yellow-300">
                  Room: {currentRoomGameData?.title || `...${activeRoomId?.slice(-6)}`}
                </h2>
                <Button
                  onClick={handleLeaveRoom}
                  className="text-xs px-2 py-1 bg-red-600 hover:bg-red-700"
                >
                  Leave
                </Button>
              </div>
              <ConnectionStatusIndicator status={connectionStatus} />
              {errorMp && (
                <p className="text-red-400 bg-red-900 p-2 rounded text-xs mb-2 text-center">{errorMp}</p>
              )}

              {/* Room Status and Ready Button */}
              {currentRoomGameData?.status === 'waiting' && (
                <div className="mb-3">
                  <p className="text-sm mb-2">
                    Status: {(() => {
                      const readyCount = playersInRoom.filter(p => p.is_ready).length;
                      const totalCount = playersInRoom.length;
                      const displayText = `${readyCount}/${totalCount} ready`;
                      return displayText;
                    })()}:
                  </p>
                  {/* Show "Ready Up" button only if game is waiting */}
                  {user && playersInRoom.some(p => p.user_id === user.id) && (
                    <Button
                      onClick={handleToggleReady}
                      disabled={isSubmittingReady || !isOnline}
                      title={!isOnline ? "You are offline." : ""}
                      className={cn(
                        "text-xs px-2 py-1 transition-colors",
                        (isSubmittingReady || !isOnline)
                          ? "bg-gray-400 cursor-not-allowed"
                          : playersInRoom.find(p => p.user_id === user.id)?.is_ready
                            ? "bg-green-600 hover:bg-green-700"
                            : "bg-yellow-600 hover:bg-yellow-700"
                      )}
                    >
                      {isSubmittingReady
                        ? "Processing..."
                        : playersInRoom.find(p => p.user_id === user.id)?.is_ready
                          ? "Ready ✓"
                          : "Ready Up"}
                    </Button>
                  )}
                </div>
              )}

              {/* Player List */}
              <div className="flex-1 overflow-y-auto mb-3">
                {playersInRoom.map(player => (
                  <div key={player.user_id} className={cn(
                    "text-xs p-1.5 rounded mb-1 flex justify-between items-center",
                    "bg-slate-700/50 hover:bg-slate-700/70 transition-colors",
                    player.user_id === user?.id && "bg-slate-600/70"
                  )}>
                    <span className="truncate">
                      {player.profile?.username || `Player...${player.user_id.slice(-4)}`}
                      {player.user_id === user?.id && " (You)"}
                      {(currentRoomGameData?.host_id === player.user_id || (!currentRoomGameData && gameRooms.find(r => r.id === activeRoomId)?.host_id === player.user_id)) && " (Host)"}
                      {!player.is_connected && <span className="ml-1 text-red-700 italic">(Disconnected)</span>}
                    </span>
                    {player.is_ready && (
                      <span className="text-green-400 ml-2">✓</span>
                    )}
                  </div>
                ))}
              </div>

              {/* Show "Start Game" button only if user is host and game is waiting */}
              {user && currentRoomGameData?.host_id === user.id && currentRoomGameData?.status === 'waiting' && (
                <Button
                  onClick={handleStartGame}
                  disabled={
                    isRoomLoading ||
                    isStartingGame ||
                    !playersInRoom.every(p => p.is_ready) ||
                    playersInRoom.length < 2 ||
                    !isOnline
                  }
                  title={!isOnline ? "You are offline. Please check your connection." : ""}
                  className={cn(
                    "w-full mt-2 mb-2 py-2 text-lg font-bold",
                    "bg-green-600 hover:bg-green-700 text-white",
                    (isRoomLoading || isStartingGame || !playersInRoom.every(p => p.is_ready) || playersInRoom.length < 2 || !isOnline) && "opacity-50 cursor-not-allowed"
                  )}
                >
                  {(() => {
                    const playerCount = playersInRoom.length;
                    const needMorePlayers = playerCount < 2;
                    const playersNeeded = needMorePlayers ? 2 - playerCount : 0;
                    const allReady = playersInRoom.every(p => p.is_ready);

                    const buttonText = isRoomLoading
                      ? "Connecting..."
                      : isStartingGame
                        ? "Starting Game..."
                        : needMorePlayers
                          ? `Need ${playersNeeded} more player(s)`
                          : !allReady
                            ? "Waiting for all to ready..."
                            : "Start Game";

                    return buttonText;
                  })()}
                </Button>
              )}
            </div>
          )}
        </div>

        {/* Center Panel */}
        <div className="md:col-span-1 bg-green-900 bg-opacity-75 p-6 rounded-lg border border-green-700 text-white shadow-lg h-[600px] flex flex-col items-center justify-center">
          {selectedOverallGameType === 'single-player' ? (
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-4">Single Player Mode</h2>
              <p className="text-gray-300">Single player functionality coming soon!</p>
            </div>
          ) : (
            <div className="text-center">
              <h2 className="text-2xl font-bold mb-4">Multiplayer Mode</h2>
              {multiplayerPanelState === 'lobby_list' ? (
                <p className="text-gray-300">Select a room to join or create a new one</p>
              ) : currentRoomGameData?.status === 'active' && currentRoomGameData.current_question_data ? (
                <div className="w-full">
                  <h3 className="text-lg font-bold mb-4">Game Active!</h3>
                  <div className="grid grid-cols-2 gap-2">
                    {currentRoomGameData.current_question_data.choices?.map((choice: any, index: number) => (
                      <Button
                        key={index}
                        onClick={() => handleMultiplayerAnswerSubmit(choice.name)}
                        disabled={hasAnswered || isSubmittingAnswer}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        {choice.name}
                      </Button>
                    ))}
                  </div>
                  {optimisticAnswer && (
                    <div className="mt-4 p-2 bg-green-800 rounded">
                      <p>Answer: {optimisticAnswer.choiceName}</p>
                      <p>Correct: {optimisticAnswer.isCorrect ? 'Yes' : 'No'}</p>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-gray-300">Waiting for game to start...</p>
              )}
            </div>
          )}
        </div>

        {/* Right Panel */}
        <div className="md:col-span-1 bg-green-900 bg-opacity-75 pt-5 px-3 pb-3 rounded-lg border border-green-700 text-white shadow-lg h-[600px] flex flex-col">
          {selectedOverallGameType === 'single-player' ? (
            <PlayerInfoPanel player={null} />
          ) : (
            <div className="flex flex-col h-full">
              <h2 className="text-2xl font-bold mb-3 text-center text-yellow-300 border-b-2 border-yellow-500 pb-2">Game Info</h2>
              <div className="text-center text-gray-300">
                <p>Multiplayer game information will appear here</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Global Animations */}
      {globalAnimations.map(animation => (
        animation.type === 'enhanced' ? (
          <GlobalMultiplayerScoreAnimation
            key={animation.id}
            trigger={animation.trigger}
            scoreIncrease={animation.scoreIncrease || 10}
            bonusLevel={animation.bonusLevel || 0}
            originPosition={animation.originPosition}
          />
        ) : (
          <GlobalFallbackAnimation
            key={animation.id}
            animationKey={animation.id}
            originPosition={animation.originPosition}
          />
        )
      ))}
    </main>
  );
}

// New default export that wraps HomePageContent with Suspense
export default function HomePage() {
  return (
    <Suspense fallback={<div className="flex min-h-screen items-center justify-center text-white bg-gray-900">Loading page...</div>}>
      <HomePageContent />
    </Suspense>
  );
}